// Extended type to include all possible field types used in formatting
interface ExtendedDynamicFieldSchema {
  label: string;
  key: string;
  type:
    | "text"
    | "number"
    | "dropdown"
    | "multi-select"
    | "date"
    | "boolean"
    | "number-range"
    | "hotels"
    | "destinations"
    | "addons"
    | "date-range"
    | "select"
    | "textarea";
  options?: (string | { value: string; label: string })[];
  required: boolean;
  used_in_filtering?: boolean;
  used_in_supplier_offering?: boolean;
  used_in_product?: boolean;
  locked_in_offerings?: boolean;
}

// Types for hotels and destinations data
interface Hotel {
  id: string;
  name: string;
}

interface Destination {
  id: string;
  name: string;
}

interface ProductService {
  id: string;
  name: string;
  category?: {
    name: string;
  };
  type: string;
}

interface FormatOptions {
  hotels?: Hotel[];
  destinations?: Destination[];
  productServices?: ProductService[];
}

/**
 * Formats a custom field value for display based on its field type
 * @param value - The raw value from the database
 * @param fieldSchema - The field schema containing type and options information
 * @param options - Additional data needed for formatting (hotels, destinations, productServices)
 * @returns Formatted string for display
 */
export function formatCustomFieldValue(
  value: any,
  fieldSchema?: ExtendedDynamicFieldSchema,
  options?: FormatOptions
): string {
  // Handle null/undefined values
  if (value === null || value === undefined) {
    return "—";
  }

  // Handle empty strings
  if (value === "") {
    return "—";
  }

  // Handle arrays (multi-select fields)
  if (Array.isArray(value)) {
    if (value.length === 0) {
      return "—";
    }
    return value.join(", ");
  }

  // Handle based on field type
  switch (fieldSchema?.type) {
    case "boolean":
      return value ? "Yes" : "No";

    case "date":
      try {
        return new Date(value).toLocaleDateString("de-CH");
      } catch {
        return value.toString();
      }

    case "number-range":
      if (typeof value === "object" && value !== null) {
        const { min, max } = value;
        if (min !== undefined && max !== undefined) {
          return `${min} - ${max}`;
        } else if (min !== undefined) {
          return `${min}+`;
        } else if (max !== undefined) {
          return `Up to ${max}`;
        }
      }
      // Handle string format like "5-12"
      if (typeof value === "string" && value.includes("-")) {
        return value;
      }
      return "—";

    case "date-range":
      if (typeof value === "object" && value !== null) {
        const { from, to } = value;
        try {
          if (from && to) {
            const fromDate = new Date(from).toLocaleDateString("de-CH");
            const toDate = new Date(to).toLocaleDateString("de-CH");
            return `${fromDate} - ${toDate}`;
          } else if (from) {
            return `From ${new Date(from).toLocaleDateString("de-CH")}`;
          } else if (to) {
            return `Until ${new Date(to).toLocaleDateString("de-CH")}`;
          }
        } catch {
          // Fall through to default handling
        }
      }
      return "—";

    case "dropdown":
    case "select":
      // Handle dropdown fields - show the display value if available
      if (fieldSchema?.options && Array.isArray(fieldSchema.options)) {
        const option = fieldSchema.options.find(
          (opt) => (typeof opt === "object" ? opt.value : opt) === value
        );
        if (option) {
          return typeof option === "object" ? option.label : option;
        }
      }
      return value.toString();

    case "hotels":
      // Handle both array and JSON string formats
      let hotelIds: string[] = [];
      if (Array.isArray(value)) {
        hotelIds = value;
      } else if (typeof value === "string") {
        try {
          const parsed = JSON.parse(value);
          hotelIds = Array.isArray(parsed) ? parsed : [parsed];
        } catch {
          // If parsing fails, treat as single ID
          hotelIds = [value];
        }
      } else {
        hotelIds = [value.toString()];
      }

      if (hotelIds.length > 0 && options?.hotels) {
        const hotelNames = hotelIds
          .map((hotelId) => {
            const found = options.hotels?.find((hotel) => hotel.id === hotelId);
            return found?.name;
          })
          .filter(Boolean);

        if (hotelNames.length > 0) {
          return hotelNames.join(", ");
        }
        return hotelIds.join(", ");
      }
      return hotelIds.join(", ");

    case "destinations":
      // Handle both array and JSON string formats
      let destinationIds: string[] = [];
      if (Array.isArray(value)) {
        destinationIds = value;
      } else if (typeof value === "string") {
        try {
          const parsed = JSON.parse(value);
          destinationIds = Array.isArray(parsed) ? parsed : [parsed];
        } catch {
          // If parsing fails, treat as single ID
          destinationIds = [value];
        }
      } else {
        destinationIds = [value.toString()];
      }

      if (destinationIds.length > 0 && options?.destinations) {
        const destinationNames = destinationIds
          .map((destId) => {
            const found = options.destinations?.find(
              (dest) => dest.id === destId
            );
            return found?.name;
          })
          .filter(Boolean);

        if (destinationNames.length > 0) {
          return destinationNames.join(", ");
        }
        return destinationIds.join(", ");
      }
      return destinationIds.join(", ");

    case "addons":
      // Handle both array and JSON string formats
      let addonIds: string[] = [];
      if (Array.isArray(value)) {
        addonIds = value;
      } else if (typeof value === "string") {
        try {
          const parsed = JSON.parse(value);
          addonIds = Array.isArray(parsed) ? parsed : [parsed];
        } catch {
          // If parsing fails, treat as single ID
          addonIds = [value];
        }
      } else {
        addonIds = [value.toString()];
      }

      if (addonIds.length > 0 && options?.productServices) {
        const addonNames = addonIds
          .map((addonId) => {
            const found = options.productServices?.find(
              (addon) => addon.id === addonId
            );
            return found?.name;
          })
          .filter(Boolean);

        if (addonNames.length > 0) {
          return addonNames.join(", ");
        }
        return addonIds.join(", ");
      }
      return addonIds.join(", ");

    case "number":
      if (typeof value === "number") {
        return value.toString();
      }
      if (typeof value === "string" && !isNaN(Number(value))) {
        return value;
      }
      return value.toString();

    case "text":
    case "textarea":
      return value.toString();

    default:
      // Handle unknown field types or complex objects
      if (typeof value === "object") {
        try {
          // For debugging purposes, show the JSON structure
          return JSON.stringify(value, null, 2);
        } catch {
          return "[Complex Object]";
        }
      }
      return value.toString();
  }
}

/**
 * Formats multiple custom field values for display
 * @param customFields - Object containing custom field values
 * @param fieldSchemas - Array of field schemas
 * @param options - Additional data needed for formatting (hotels, destinations, productServices)
 * @returns Object with formatted values
 */
export function formatCustomFields(
  customFields: Record<string, any>,
  fieldSchemas: ExtendedDynamicFieldSchema[],
  options?: FormatOptions
): Record<string, { label: string; value: string; type?: string }> {
  const formatted: Record<
    string,
    { label: string; value: string; type?: string }
  > = {};

  Object.entries(customFields).forEach(([key, value]) => {
    const fieldSchema = fieldSchemas.find((field) => field.key === key);
    const fieldLabel =
      fieldSchema?.label ||
      key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, " ");

    formatted[key] = {
      label: fieldLabel,
      value: formatCustomFieldValue(value, fieldSchema, options),
      type: fieldSchema?.type,
    };
  });

  return formatted;
}

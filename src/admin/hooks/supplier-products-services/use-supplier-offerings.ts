import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "@camped-ai/ui";
import { useState } from "react";
import * as XLSX from "xlsx";

// Utility function to parse various date formats from Excel
const parseExcelDate = (dateValue: any): string | null => {
  if (!dateValue || dateValue === "") return null;

  try {
    // If it's already a Date object
    if (dateValue instanceof Date) {
      return dateValue.toISOString().split("T")[0];
    }

    // If it's a number (Excel serial date)
    if (typeof dateValue === "number") {
      // Use XLSX library's built-in date conversion for accuracy
      try {
        const excelDate = XLSX.SSF.parse_date_code(dateValue);
        if (excelDate) {
          // Create JavaScript date from Excel date components in UTC to avoid timezone shifts
          const jsDate = new Date(Date.UTC(excelDate.y, excelDate.m - 1, excelDate.d));

          const result = jsDate.toISOString().split("T")[0];
          return result;
        }
      } catch (error) {
        console.log("🔍 XLSX date conversion failed, using fallback");
      }

      // Fallback: Manual Excel serial date conversion
      // Excel epoch: December 30, 1899 (day 0)
      const msPerDay = 24 * 60 * 60 * 1000;
      const excelEpoch = new Date(1899, 11, 30).getTime();
      let adjustedSerial = dateValue;

      // Account for Excel's leap year bug (treats 1900 as leap year)
      if (dateValue > 59) {
        adjustedSerial -= 1;
      }

      const jsDate = new Date(excelEpoch + adjustedSerial * msPerDay);
      const result = jsDate.toISOString().split("T")[0];
      return result;
    }

    // If it's a string, try various formats
    if (typeof dateValue === "string") {
      const trimmedValue = dateValue.trim();

      // Handle Excel date objects that have been converted to strings
      if (trimmedValue.includes("GMT") || trimmedValue.includes("Time")) {
        const date = new Date(trimmedValue);
        if (!isNaN(date.getTime())) {
          return date.toISOString().split("T")[0];
        }
      }

      // Try common date formats
      const formats = [
        // ISO format (YYYY-MM-DD)
        /^(\d{4})-(\d{1,2})-(\d{1,2})$/,
        // European format (DD/MM/YYYY)
        /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,
        // US format (MM/DD/YYYY)
        /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,
        // Dot format (DD.MM.YYYY)
        /^(\d{1,2})\.(\d{1,2})\.(\d{4})$/,
      ];

      // Try ISO format first
      const isoMatch = trimmedValue.match(formats[0]);
      if (isoMatch) {
        const [, year, month, day] = isoMatch;
        // Fix: JavaScript Date constructor uses 0-based months (0 = January)
        // Use UTC to avoid timezone shifts
        const date = new Date(Date.UTC(parseInt(year), parseInt(month) - 1, parseInt(day)));
        if (!isNaN(date.getTime())) {
          return date.toISOString().split("T")[0];
        }
      }

      // Try European format (DD/MM/YYYY)
      const euroMatch = trimmedValue.match(formats[1]);
      if (euroMatch) {
        const [, day, month, year] = euroMatch;
        // Fix: JavaScript Date constructor uses 0-based months (0 = January)
        // Use UTC to avoid timezone shifts
        const date = new Date(Date.UTC(parseInt(year), parseInt(month) - 1, parseInt(day)));
        if (!isNaN(date.getTime())) {
          return date.toISOString().split("T")[0];
        }
      }

      // Try dot format (DD.MM.YYYY)
      const dotMatch = trimmedValue.match(formats[3]);
      if (dotMatch) {
        const [, day, month, year] = dotMatch;
        // Fix: JavaScript Date constructor uses 0-based months (0 = January)
        // Use UTC to avoid timezone shifts
        const date = new Date(Date.UTC(parseInt(year), parseInt(month) - 1, parseInt(day)));
        if (!isNaN(date.getTime())) {
          return date.toISOString().split("T")[0];
        }
      }

      // Fallback: try native Date parsing
      const fallbackDate = new Date(trimmedValue);
      if (!isNaN(fallbackDate.getTime())) {
        const result = fallbackDate.toISOString().split("T")[0];
        return result;
      }
    }
    return null;
  } catch (error) {
    console.log("🔍 parseExcelDate - Error occurred:", error);
    return null;
  }
};

export interface SupplierOffering {
  id: string;
  product_service_id: string;
  supplier_id: string;
  active_from?: string;
  active_to?: string;
  availability_notes?: string;
  cost?: number;
  currency?: string;
  currency_override?: boolean;

  // Enhanced pricing fields (can be strings or numbers from API)
  commission?: string | number;
  public_price?: string | number;
  gross_price?: string | number;
  net_cost?: string | number;
  net_price?: string | number;
  margin_rate?: string | number;
  selling_price?: string | number;
  custom_prices?: Array<{ name: string; price: number }>;

  // Selling currency fields
  selling_currency?: string;
  selling_price_selling_currency?: string | number;
  exchange_rate?: string | number;
  exchange_rate_date?: string;

  status: "active" | "inactive";
  custom_fields?: Record<string, any>;
  add_ons?: any[];
  created_by?: string;
  updated_by?: string;
  created_at: string;
  updated_at: string;
  product_service?: {
    id: string;
    name: string;
    type: string;
    description?: string;
    category?: {
      id: string;
      name: string;
      dynamic_field_schema?: any[];
    };
  };
  supplier?: {
    id: string;
    name: string;
    type: string;
    status: string;
    primary_contact_name?: string;
    primary_contact_email?: string;
    default_currency?: string;
  };
}

export interface CreateSupplierOfferingData {
  product_service_id: string;
  supplier_id: string;
  active_from?: string | null;
  active_to?: string | null;
  availability_notes?: string | null;
  cost?: number | null;
  currency?: string | null;
  currency_override?: boolean;
  status?: "active" | "inactive";
  custom_fields?: Record<string, any>;
  add_ons?: any[]; // Array of addon line items

  // Enhanced pricing fields for duplication support (converted to numbers)
  commission?: number | null;
  gross_price?: number | null;
  net_cost?: number | null;
  net_price?: number | null;
  margin_rate?: number | null;
  selling_price?: number | null;

  // Selling currency fields for duplication support
  selling_currency?: string | null;
  selling_price_selling_currency?: number | null;
  exchange_rate?: number | null;
  exchange_rate_date?: string | null;
}

export interface UpdateSupplierOfferingData {
  product_service_id?: string;
  supplier_id?: string;
  active_from?: string | null;
  active_to?: string | null;
  availability_notes?: string | null;
  cost?: number | null;
  currency?: string | null;
  currency_override?: boolean;
  status?: "active" | "inactive";
  custom_fields?: Record<string, any>;
  add_ons?: any[]; // Array of addon line items
}

export interface SupplierOfferingFilters {
  supplier_id?: string;
  product_service_id?: string;
  category_id?: string;
  status?: "active" | "inactive";
  active_from?: string;
  active_to?: string;
  created_by?: string;
  updated_by?: string;
  search?: string;
  limit?: number;
  offset?: number;
  sort_by?: "product_service_name" | "net_price" | "validity" | "updated_at";
  sort_order?: "asc" | "desc";
}

// Query Keys
const SUPPLIER_OFFERINGS_QUERY_KEY = "supplier-offerings";

// List Supplier Offerings
export const useSupplierOfferings = (filters: SupplierOfferingFilters = {}) => {
  return useQuery({
    queryKey: [SUPPLIER_OFFERINGS_QUERY_KEY, filters],
    queryFn: async () => {
      const params = new URLSearchParams();

      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          // Handle Date objects by converting to ISO string (date only)
          if (value instanceof Date) {
            params.append(key, value.toISOString().split("T")[0]);
          } else {
            params.append(key, value.toString());
          }
        }
      });

      const response = await fetch(
        `/admin/supplier-management/supplier-offerings?${params.toString()}`
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to fetch supplier offerings");
      }

      const data = await response.json();

      // Fetch supplier information separately to enrich the offerings
      const supplierIds = [
        ...new Set(
          data.supplier_offerings
            ?.map((offering: any) => offering.supplier_id)
            .filter(Boolean)
        ),
      ];

      if (supplierIds.length > 0) {
        try {
          const suppliersResponse = await fetch(
            "/admin/supplier-management/suppliers"
          );
          if (suppliersResponse.ok) {
            const suppliersData = await suppliersResponse.json();
            const suppliers = suppliersData.suppliers || [];

            // Create a map of supplier ID to supplier data
            const supplierMap = new Map(
              suppliers.map((supplier: any) => [supplier.id, supplier])
            );

            // Enrich offerings with supplier information
            data.supplier_offerings = data.supplier_offerings?.map(
              (offering: any) => {
                const supplier: any = supplierMap.get(offering.supplier_id);
                if (supplier) {
                  return {
                    ...offering,
                    supplier: {
                      id: supplier.id,
                      name: supplier.name,
                      type: supplier.type,
                      status: supplier.status,
                      primary_contact_name: supplier.primary_contact_name,
                      primary_contact_email: supplier.primary_contact_email,
                    },
                  };
                }
                return offering;
              }
            );
          }
        } catch (error) {
          console.warn("Failed to fetch supplier information:", error);
        }
      }

      return data;
    },
  });
};

// Get Single Supplier Offering
export const useSupplierOffering = (id: string) => {
  return useQuery({
    queryKey: [SUPPLIER_OFFERINGS_QUERY_KEY, id],
    queryFn: async () => {
      const response = await fetch(
        `/admin/supplier-management/supplier-offerings/${id}`
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to fetch supplier offering");
      }

      const data = await response.json();
      const offering = data.supplier_offering;

      // Debug logging removed

      // Fetch supplier information separately to enrich the offering
      if (offering?.supplier_id) {
        try {
          const suppliersResponse = await fetch(
            "/admin/supplier-management/suppliers"
          );
          if (suppliersResponse.ok) {
            const suppliersData = await suppliersResponse.json();
            const suppliers = suppliersData.suppliers || [];

            // Find the supplier for this offering
            const supplier = suppliers.find(
              (s: any) => s.id === offering.supplier_id
            );
            if (supplier) {
              offering.supplier = {
                id: supplier.id,
                name: supplier.name,
                type: supplier.type,
                status: supplier.status,
                primary_contact_name: supplier.primary_contact_name,
                primary_contact_email: supplier.primary_contact_email,
                default_currency: supplier.default_currency,
                metadata: supplier.metadata || {},
              };
            }
          }
        } catch (error) {
          console.warn("Failed to fetch supplier information:", error);
        }
      }

      return data;
    },
    enabled: !!id,
    staleTime: 0, // Always consider data stale to ensure fresh fetches
    refetchOnMount: true, // Always refetch when component mounts
    refetchOnWindowFocus: true, // Refetch when window regains focus
  });
};

// Create Supplier Offering
export const useCreateSupplierOffering = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateSupplierOfferingData) => {
      const response = await fetch(
        "/admin/supplier-management/supplier-offerings",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to create supplier offering");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [SUPPLIER_OFFERINGS_QUERY_KEY],
      });
      toast.success("Supplier offering created successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create supplier offering");
    },
  });
};

// Update Supplier Offering
export const useUpdateSupplierOffering = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: UpdateSupplierOfferingData;
    }) => {
      const response = await fetch(
        `/admin/supplier-management/supplier-offerings/${id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to update supplier offering");
      }

      return response.json();
    },
    onSuccess: async (updatedData, { id }) => {
      // Remove the specific offering from cache to force fresh fetch
      queryClient.removeQueries({
        queryKey: [SUPPLIER_OFFERINGS_QUERY_KEY, id],
      });

      // Invalidate all related queries to ensure fresh data
      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: [SUPPLIER_OFFERINGS_QUERY_KEY],
        }),
        queryClient.invalidateQueries({
          queryKey: [SUPPLIER_OFFERINGS_QUERY_KEY, id],
        }),
        queryClient.invalidateQueries({
          queryKey: ["supplier-offering-cost-history", id],
        }),
      ]);

      // Force refetch of the specific offering
      await queryClient.refetchQueries({
        queryKey: [SUPPLIER_OFFERINGS_QUERY_KEY, id],
      });

      // Don't show success toast here - let the component handle it
      // This prevents showing both success and error toasts simultaneously
    },
    onError: (error: Error) => {
      // Don't show error toast here - let the component handle it
      // This prevents duplicate error messages
    },
  });
};

// Delete Supplier Offering
export const useDeleteSupplierOffering = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(
        `/admin/supplier-management/supplier-offerings/${id}`,
        {
          method: "DELETE",
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to delete supplier offering");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [SUPPLIER_OFFERINGS_QUERY_KEY],
      });
      toast.success("Supplier offering deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to delete supplier offering");
    },
  });
};

// Duplicate Supplier Offering
export const useDuplicateSupplierOffering = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      // First get the existing offering
      const getResponse = await fetch(
        `/admin/supplier-management/supplier-offerings/${id}`
      );

      if (!getResponse.ok) {
        const error = await getResponse.json();
        throw new Error(
          error.message || "Failed to fetch supplier offering for duplication"
        );
      }

      const { supplier_offering } = await getResponse.json();

      // Helper function to safely convert string/number to number or null
      const parseNumericValue = (value: string | number | null | undefined): number | null => {
        if (value === null || value === undefined || value === '') return null;
        const parsed = typeof value === 'string' ? parseFloat(value) : value;
        return isNaN(parsed) ? null : parsed;
      };

      // Create a minimal duplicate with only essential fields first
      // We'll build this up incrementally to identify what's causing the error
      const duplicateData: CreateSupplierOfferingData = {
        product_service_id: supplier_offering.product_service_id,
        supplier_id: supplier_offering.supplier_id,
        status: supplier_offering.status || "active",

        // Include pricing fields that were in the original payload
        commission: parseNumericValue(supplier_offering.commission),
        gross_price: parseNumericValue(supplier_offering.gross_price || supplier_offering.public_price),
        net_cost: parseNumericValue(supplier_offering.net_cost),
        margin_rate: parseNumericValue(supplier_offering.margin_rate),
        selling_price: parseNumericValue(supplier_offering.selling_price),

        // Include currency fields
        currency: supplier_offering.currency,
        currency_override: supplier_offering.currency_override || false,
        selling_currency: supplier_offering.selling_currency,
        selling_price_selling_currency: parseNumericValue(supplier_offering.selling_price_selling_currency),
        exchange_rate: parseNumericValue(supplier_offering.exchange_rate),

        // Include other essential fields
        availability_notes: supplier_offering.availability_notes,
        custom_fields: supplier_offering.custom_fields || {},
        add_ons: supplier_offering.add_ons || [],
      };

      // Clean up any null/undefined values that might cause validation issues
      Object.keys(duplicateData).forEach(key => {
        if (duplicateData[key] === null || duplicateData[key] === undefined) {
          delete duplicateData[key];
        }
      });

      // Log the payload for debugging
      console.log("Duplication payload:", JSON.stringify(duplicateData, null, 2));

      const createResponse = await fetch(
        "/admin/supplier-management/supplier-offerings",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(duplicateData),
        }
      );

      // Log the full response for debugging
      console.log("Response status:", createResponse.status);
      console.log("Response headers:", Object.fromEntries(createResponse.headers.entries()));

      if (!createResponse.ok) {
        const responseText = await createResponse.text();
        console.error("Raw response text:", responseText);

        let error;
        try {
          error = JSON.parse(responseText);
        } catch (parseError) {
          console.error("Failed to parse error response as JSON:", parseError);
          throw new Error(`Server returned ${createResponse.status}: ${responseText}`);
        }

        console.error("Duplication error response:", error);
        console.error("Failed payload:", JSON.stringify(duplicateData, null, 2));

        // Provide more specific error messages based on the error type
        if (error.type === "invalid_data" && error.details) {
          const validationErrors = error.details.map((detail: any) =>
            `${detail.path?.join('.')}: ${detail.message}`
          ).join(', ');
          throw new Error(`Validation failed: ${validationErrors}`);
        }

        throw new Error(
          error.message || error.details || `Server error: ${error.type || 'unknown'}`
        );
      }

      return createResponse.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [SUPPLIER_OFFERINGS_QUERY_KEY],
      });
      toast.success("Supplier offering duplicated successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to duplicate supplier offering");
    },
  });
};

// Import/Export Types
export interface ImportValidationError {
  row: number;
  field: string;
  message: string;
  value?: any;
}

export interface ImportProgress {
  processed: number;
  total: number;
  percentage: number;
}

export interface ImportResult {
  success: boolean;
  message: string;
  created: number;
  errors: ImportValidationError[];
}

// CSV conversion utility
const convertToCSV = (data: any[]): string => {
  if (!data || data.length === 0) return "";

  const headers = Object.keys(data[0]);
  const csvRows = [
    headers.join(","),
    ...data.map((row) =>
      headers
        .map((header) => {
          const value = row[header];
          // Handle values that might contain commas or quotes
          if (
            typeof value === "string" &&
            (value.includes(",") || value.includes('"') || value.includes("\n"))
          ) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value ?? "";
        })
        .join(",")
    ),
  ];

  return csvRows.join("\n");
};

// Template generation utility
export const generateSupplierOfferingTemplate = async (): Promise<any[]> => {
  try {
    // Fetch required data for template generation
    const [suppliersResponse, productsServicesResponse, categoriesResponse] =
      await Promise.all([
        fetch("/admin/supplier-management/suppliers?limit=100", {
          credentials: "include",
        }),
        fetch("/admin/supplier-management/products-services?limit=100", {
          credentials: "include",
        }),
        fetch(
          "/admin/supplier-management/products-services/categories?limit=100",
          { credentials: "include" }
        ),
      ]);

    if (
      !suppliersResponse.ok ||
      !productsServicesResponse.ok ||
      !categoriesResponse.ok
    ) {
      throw new Error("Failed to fetch required data for template generation");
    }

    const [suppliersData, productsServicesData, categoriesData] =
      await Promise.all([
        suppliersResponse.json(),
        productsServicesResponse.json(),
        categoriesResponse.json(),
      ]);

    const suppliers = suppliersData.suppliers || [];
    const productsServices = productsServicesData.product_services || [];
    const categories = categoriesData.categories || [];

    if (
      suppliers.length === 0 ||
      productsServices.length === 0 ||
      categories.length === 0
    ) {
      throw new Error(
        "Insufficient data: Please ensure you have suppliers, products/services, and categories created before generating template"
      );
    }

    const templateData: any[] = [];

    // Group products/services by category to ensure proper relationships
    const productsByCategory = new Map<string, any[]>();
    productsServices.forEach((ps: any) => {
      const categoryId = ps.category_id;
      if (!productsByCategory.has(categoryId)) {
        productsByCategory.set(categoryId, []);
      }
      productsByCategory.get(categoryId)!.push(ps);
    });

    // Generate comprehensive template records using real data relationships
    const maxExamples = Math.min(6, suppliers.length * 2); // More examples for better variety
    let exampleCount = 0;

    // Iterate through categories to ensure we cover different category types
    for (const category of categories) {
      if (exampleCount >= maxExamples) break;

      const categoryProducts = productsByCategory.get(category.id) || [];
      if (categoryProducts.length === 0) continue;

      // Create 1-2 examples per category with different suppliers
      const examplesPerCategory = Math.min(
        2,
        suppliers.length,
        categoryProducts.length
      );

      for (
        let i = 0;
        i < examplesPerCategory && exampleCount < maxExamples;
        i++
      ) {
        const supplier = suppliers[exampleCount % suppliers.length];
        const productService = categoryProducts[i % categoryProducts.length];

        // Generate realistic cost based on category and supplier
        const baseCost =
          category.category_type === "Service"
            ? category.name.includes("Transportation")
              ? 250
              : category.name.includes("Kids")
              ? 80
              : 150
            : category.name.includes("Accommodation")
            ? 180
            : 120;

        const costVariation = exampleCount * 25 + i * 15; // Add variation
        const finalCost = baseCost + costVariation;

        // Generate realistic date ranges
        const startDates = [
          "2024-01-01",
          "2024-03-01",
          "2024-06-01",
          "2024-09-01",
        ];
        const endDates = [
          "2024-12-31",
          "2025-02-28",
          "2025-05-31",
          "2025-08-31",
        ];
        const dateIndex = exampleCount % startDates.length;

        // Base template record with correct pricing calculator fields
        const baseGrossPrice = finalCost * 1.2; // Use finalCost as base for gross price
        const commission = 0.15; // 15% commission
        const marginRate = 0.25; // 25% margin
        const exchangeRate = 1.0;

        // Calculate example values following pricing calculator logic
        const calculatedNetCost = baseGrossPrice - (baseGrossPrice * commission);
        const calculatedSellingPrice = calculatedNetCost / (1 - marginRate);
        const sellingPriceInSellingCurrency = calculatedSellingPrice * exchangeRate;

        const templateRecord: any = {
          supplier_name: supplier.name,
          product_service_name: productService.name,
          category_name: category.name,
          active_from: startDates[dateIndex],
          active_to: endDates[dateIndex],

          // Pricing fields (INPUT FIELDS ONLY - following pricing calculator structure)
          gross_price: baseGrossPrice.toFixed(2),     // User input - Gross price
          commission: (commission * 100).toString(),   // User input - Commission percentage (15)
          net_cost_manual: '',                        // User input - Manual override (leave empty)
          margin_rate: (marginRate * 100).toString(), // User input - Margin rate percentage (25)

          // Currency fields
          currency: supplier.default_currency || "CHF", // Auto-populated from supplier

          // Selling currency fields
          selling_currency: "CHF",                     // User input - Selling currency
          exchange_rate: exchangeRate.toString(),      // User input - Exchange rate
          selling_price_selling_currency: sellingPriceInSellingCurrency.toFixed(2), // Calculated but shown

          availability_notes: `${productService.name} available through ${
            supplier.name
          } - ${category.category_type.toLowerCase()}`,
          status: exampleCount % 4 === 0 ? "inactive" : "active", // Mix of active/inactive for variety
        };

        // Add dynamic fields from category schema with realistic values - only fields used in supplier offerings
        if (
          category.dynamic_field_schema &&
          Array.isArray(category.dynamic_field_schema)
        ) {
          const supplierOfferingFields = category.dynamic_field_schema.filter(
            (field: any) => field.used_in_supplier_offering === true
          );

          supplierOfferingFields.forEach((field: any) => {
            let value = "";

            // Generate realistic values based on field type and context
            switch (field.type) {
              case "dropdown":
                if (field.options && field.options.length > 0) {
                  // Rotate through options for variety
                  const optionIndex = (exampleCount + i) % field.options.length;
                  value = field.options[optionIndex];
                } else {
                  value = "Standard";
                }
                break;

              case "multi-select":
                if (field.options && field.options.length > 0) {
                  // Select 1-2 options for variety
                  const numSelections = Math.min(2, field.options.length);
                  const startIndex = (exampleCount + i) % field.options.length;
                  const selectedOptions = [];
                  for (let j = 0; j < numSelections; j++) {
                    const optionIndex = (startIndex + j) % field.options.length;
                    selectedOptions.push(field.options[optionIndex]);
                  }
                  value = selectedOptions.join(",");
                } else {
                  value = "Option A,Option B";
                }
                break;

              case "number":
                // Generate contextual numbers based on field name
                if (
                  field.key.includes("capacity") ||
                  field.key.includes("max")
                ) {
                  value = String(20 + exampleCount * 5);
                } else if (
                  field.key.includes("age") ||
                  field.key.includes("min")
                ) {
                  value = String(3 + i * 2);
                } else if (field.key.includes("duration")) {
                  value = String(60 + exampleCount * 30);
                } else {
                  value = String(10 + exampleCount * 5);
                }
                break;

              case "text":
                // Generate contextual text based on field name and category
                if (field.key.includes("description")) {
                  value = `${category.name} ${productService.name} - detailed information`;
                } else if (field.key.includes("requirement")) {
                  value = `Special requirements for ${productService.name}`;
                } else if (field.key.includes("note")) {
                  value = `Additional notes for ${category.name} service`;
                } else {
                  value = `${field.label || field.key} value for ${
                    productService.name
                  }`;
                }
                break;

              case "boolean":
                // Alternate between true/false for variety
                value = (exampleCount + i) % 2 === 0 ? "true" : "false";
                break;

              case "date":
                // Generate dates relative to the offering period
                const dates = [
                  "2024-01-15",
                  "2024-04-01",
                  "2024-07-15",
                  "2024-10-01",
                ];
                value = dates[(exampleCount + i) % dates.length];
                break;

              case "number-range":
                const minVal = 1 + i * 2;
                const maxVal = minVal + 5 + exampleCount * 2;
                value = `${minVal}-${maxVal}`;
                break;

              default:
                value = `Sample ${field.type} value`;
            }

            templateRecord[`custom_field_${field.key}`] = value;
          });
        }

        templateData.push(templateRecord);
        exampleCount++;
      }
    }

    return templateData;
  } catch (error) {
    // Return comprehensive fallback template if API calls fail
    return [
      {
        supplier_name: "Alpine Adventure Tours",
        product_service_name: "Mountain Hiking Guide Service",
        category_name: "Transportation",
        active_from: "2024-01-01",
        active_to: "2024-12-31",

        // Pricing fields (INPUT FIELDS ONLY - following pricing calculator structure)
        gross_price: "300.00",     // User input - Gross price
        commission: "15",          // User input - Commission percentage (15%)
        net_cost_manual: "",       // User input - Manual override (leave empty)
        margin_rate: "25",         // User input - Margin rate percentage (25%)

        // Currency fields
        currency: "CHF",           // Auto-populated from supplier

        // Selling currency fields
        selling_currency: "CHF",   // User input - Selling currency
        exchange_rate: "1.0",      // User input - Exchange rate
        selling_price_selling_currency: "340.00", // Calculated: 255÷(1-0.25)×1.0

        availability_notes:
          "Mountain Hiking Guide Service available through Alpine Adventure Tours - service",
        status: "active",
        custom_field_vehicle_type: "Van",
        custom_field_capacity: "8",
        custom_field_has_insurance: "true",
      },
      {
        supplier_name: "Kids Fun Center",
        product_service_name: "Children's Activity Program",
        category_name: "Kids Club",
        active_from: "2024-03-01",
        active_to: "2025-02-28",

        // Pricing fields (INPUT FIELDS ONLY - following pricing calculator structure)
        gross_price: "100.00",     // User input - Gross price
        commission: "20",          // User input - Commission percentage (20%)
        net_cost_manual: "",       // User input - Manual override (leave empty)
        margin_rate: "30",         // User input - Margin rate percentage (30%)

        // Currency fields
        currency: "EUR",           // Auto-populated from supplier

        // Selling currency fields
        selling_currency: "EUR",   // User input - Selling currency
        exchange_rate: "1.0",      // User input - Exchange rate
        selling_price_selling_currency: "114.29", // Calculated: 80÷(1-0.30)×1.0

        availability_notes:
          "Children's Activity Program available through Kids Fun Center - service",
        status: "active",
        custom_field_age_range: "3-12",
        custom_field_supervision_required: "true",
        custom_field_activity_type: "Indoor,Outdoor",
      },
      {
        supplier_name: "Luxury Hotel Partners",
        product_service_name: "Premium Suite Package",
        category_name: "Accommodation",
        active_from: "2024-06-01",
        active_to: "2025-05-31",

        // Pricing fields (INPUT FIELDS ONLY - following pricing calculator structure)
        gross_price: "205.00",     // User input - Gross price
        commission: "12",          // User input - Commission percentage (12%)
        net_cost_manual: "",       // User input - Manual override (leave empty)
        margin_rate: "22",         // User input - Margin rate percentage (22%)

        // Currency fields
        currency: "CHF",           // Auto-populated from supplier

        // Selling currency fields
        selling_currency: "CHF",   // User input - Selling currency
        exchange_rate: "1.0",      // User input - Exchange rate
        selling_price_selling_currency: "231.28", // Calculated: 180.4÷(1-0.22)×1.0

        availability_notes:
          "Premium Suite Package available through Luxury Hotel Partners - product",
        status: "active",
        custom_field_room_type: "Suite",
        custom_field_max_occupancy: "4",
        custom_field_amenities: "WiFi,Breakfast,Spa",
      },
    ];
  }
};

// Validation utility for import data
export const validateSupplierOfferingImportData = async (
  data: any[],
  suppliers: any[] = [],
  productsServices: any[] = [],
  categories: any[] = []
): Promise<ImportValidationError[]> => {
  const errors: ImportValidationError[] = [];

  try {
    data.forEach((row, index) => {
      const rowNumber = index + 2; // Excel row number (1-based + header)

      // Required field validations
      if (!row.supplier_name && !row.supplier_id) {
        errors.push({
          row: rowNumber,
          field: "supplier_name",
          message: "Supplier name or ID is required",
          value: row.supplier_name,
        });
      }

      if (!row.product_service_name && !row.product_service_id) {
        errors.push({
          row: rowNumber,
          field: "product_service_name",
          message: "Product/Service name or ID is required",
          value: row.product_service_name,
        });
      }

      // Validate supplier exists
      if (row.supplier_name || row.supplier_id) {
        const supplier = suppliers.find(
          (s) => s.name === row.supplier_name || s.id === row.supplier_id
        );

        if (!supplier) {
          // Provide helpful suggestions for similar supplier names
          const supplierName = row.supplier_name || row.supplier_id;
          const suggestions = suppliers
            .filter(
              (s) =>
                s.name.toLowerCase().includes(supplierName.toLowerCase()) ||
                supplierName.toLowerCase().includes(s.name.toLowerCase())
            )
            .slice(0, 3)
            .map((s) => s.name);

          const suggestionText =
            suggestions.length > 0
              ? ` Did you mean: ${suggestions.join(", ")}?`
              : ` Available suppliers: ${suppliers
                  .slice(0, 3)
                  .map((s) => s.name)
                  .join(", ")}`;

          errors.push({
            row: rowNumber,
            field: "supplier_name",
            message: `Supplier "${supplierName}" not found.${suggestionText}`,
            value: supplierName,
          });
        }
      }

      // Validate product/service exists
      if (row.product_service_name || row.product_service_id) {
        const productService = productsServices.find(
          (ps) =>
            ps.name === row.product_service_name ||
            ps.id === row.product_service_id
        );
        if (!productService) {
          errors.push({
            row: rowNumber,
            field: "product_service_name",
            message: `Product/Service "${
              row.product_service_name || row.product_service_id
            }" not found`,
            value: row.product_service_name || row.product_service_id,
          });
        } else {
          // Validate dynamic fields based on category
          const category = categories.find(
            (c) => c.id === productService.category_id
          );
          if (category && category.dynamic_field_schema) {
            const supplierOfferingFields = category.dynamic_field_schema.filter(
              (field: any) => field.used_in_supplier_offering === true
            );

            supplierOfferingFields.forEach((field: any) => {
              // Skip validation for locked fields - they are inherited from products/services
              if (field.locked_in_offerings === true) {
                return;
              }

              // Check both prefixed and non-prefixed field keys
              const prefixedFieldKey = `custom_field_${field.key}`;
              const fieldKey = field.key;
              const fieldValue = row[prefixedFieldKey] || row[fieldKey];

              // Required field validation (only for non-locked fields)
              if (
                field.required &&
                (!fieldValue || fieldValue.toString().trim() === "")
              ) {
                errors.push({
                  row: rowNumber,
                  field:
                    row[prefixedFieldKey] !== undefined
                      ? prefixedFieldKey
                      : fieldKey,
                  message: `${field.label} is required`,
                  value: fieldValue,
                });
              }

              // Type-specific validation (only for non-locked fields)
              if (fieldValue && fieldValue.toString().trim() !== "") {
                switch (field.type) {
                  case "number":
                    if (isNaN(parseFloat(fieldValue))) {
                      errors.push({
                        row: rowNumber,
                        field:
                          row[prefixedFieldKey] !== undefined
                            ? prefixedFieldKey
                            : fieldKey,
                        message: `${field.label} must be a valid number`,
                        value: fieldValue,
                      });
                    }
                    break;
                  case "dropdown":
                    if (field.options && !field.options.includes(fieldValue)) {
                      errors.push({
                        row: rowNumber,
                        field: fieldKey,
                        message: `${
                          field.label
                        } must be one of: ${field.options.join(", ")}`,
                        value: fieldValue,
                      });
                    }
                    break;
                  case "multi-select":
                    if (field.options) {
                      const selectedValues = fieldValue
                        .split(",")
                        .map((v: string) => v.trim());
                      const invalidValues = selectedValues.filter(
                        (v: string) => !field.options.includes(v)
                      );
                      if (invalidValues.length > 0) {
                        errors.push({
                          row: rowNumber,
                          field: fieldKey,
                          message: `${
                            field.label
                          } contains invalid options: ${invalidValues.join(
                            ", "
                          )}. Valid options: ${field.options.join(", ")}`,
                          value: fieldValue,
                        });
                      }
                    }
                    break;
                  case "boolean":
                    if (
                      !["true", "false", "1", "0", "yes", "no"].includes(
                        fieldValue.toString().toLowerCase()
                      )
                    ) {
                      errors.push({
                        row: rowNumber,
                        field: fieldKey,
                        message: `${field.label} must be a boolean value (true/false, 1/0, yes/no)`,
                        value: fieldValue,
                      });
                    }
                    break;
                  case "date":
                    const parsedCustomDate = parseExcelDate(fieldValue);
                    if (!parsedCustomDate) {
                      errors.push({
                        row: rowNumber,
                        field: fieldKey,
                        message: `${field.label} must be a valid date (supports YYYY-MM-DD, DD/MM/YYYY, DD.MM.YYYY formats)`,
                        value: fieldValue,
                      });
                    }
                    break;
                  case "number-range":
                    const rangePattern = /^\d+(-\d+)?$/;
                    if (!rangePattern.test(fieldValue.toString())) {
                      errors.push({
                        row: rowNumber,
                        field: fieldKey,
                        message: `${field.label} must be in format "min-max" or single number`,
                        value: fieldValue,
                      });
                    }
                    break;
                }
              }
            });
          }
        }
      }

      // Cost validation
      if (row.cost !== undefined && row.cost !== null && row.cost !== "") {
        const costValue = parseFloat(row.cost);
        if (isNaN(costValue) || costValue < 0) {
          errors.push({
            row: rowNumber,
            field: "cost",
            message: "Cost must be a valid positive number",
            value: row.cost,
          });
        }
      }

      // Currency validation removed - currency is auto-populated from supplier default currency

      // Date validations with better Excel date handling
      if (row.active_from && row.active_from !== "") {
        const parsedDate = parseExcelDate(row.active_from);
        if (!parsedDate) {
          errors.push({
            row: rowNumber,
            field: "active_from",
            message:
              "Active from must be a valid date (supports YYYY-MM-DD, DD/MM/YYYY, DD.MM.YYYY formats)",
            value: row.active_from,
          });
        }
      }

      if (row.active_to && row.active_to !== "") {
        const parsedDate = parseExcelDate(row.active_to);
        if (!parsedDate) {
          errors.push({
            row: rowNumber,
            field: "active_to",
            message:
              "Active to must be a valid date (supports YYYY-MM-DD, DD/MM/YYYY, DD.MM.YYYY formats)",
            value: row.active_to,
          });
        }
      }

      // Date range validation
      if (row.active_from && row.active_to) {
        const fromDate = parseExcelDate(row.active_from);
        const toDate = parseExcelDate(row.active_to);

        if (fromDate && toDate && fromDate > toDate) {
          errors.push({
            row: rowNumber,
            field: "active_to",
            message: "Active to date must be after active from date",
            value: `${row.active_from} → ${row.active_to}`,
          });
        }
      }

      // Status validation
      if (row.status && !["active", "inactive"].includes(row.status)) {
        errors.push({
          row: rowNumber,
          field: "status",
          message: 'Status must be either "active" or "inactive"',
          value: row.status,
        });
      }
    });
  } catch (error) {
    errors.push({
      row: 0,
      field: "general",
      message: "Failed to validate data due to server error",
      value: null,
    });
  }

  return errors;
};

// CSV export utility
export const exportToCSV = (data: any[], filename: string) => {
  const csvContent = convertToCSV(data);
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");
  const url = URL.createObjectURL(blob);
  link.setAttribute("href", url);
  link.setAttribute(
    "download",
    `${filename}_${new Date().toISOString().split("T")[0]}.csv`
  );
  link.style.visibility = "hidden";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

// Excel export utility
export const exportToExcel = (data: any[], filename: string) => {
  const worksheet = XLSX.utils.json_to_sheet(data);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, "Supplier_Offerings");

  // Auto-size columns
  const colWidths = Object.keys(data[0] || {}).map((key) => ({
    wch: Math.max(key.length, 15),
  }));
  worksheet["!cols"] = colWidths;

  const excelBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "array" });
  const blob = new Blob([excelBuffer], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  });
  const link = document.createElement("a");
  const url = URL.createObjectURL(blob);
  link.setAttribute("href", url);
  link.setAttribute(
    "download",
    `${filename}_${new Date().toISOString().split("T")[0]}.xlsx`
  );
  link.style.visibility = "hidden";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

// Custom hook for supplier offering import/export operations
export const useSupplierOfferingImportExport = () => {
  const [importProgress, setImportProgress] = useState<ImportProgress>({
    processed: 0,
    total: 0,
    percentage: 0,
  });
  const queryClient = useQueryClient();

  // File parsing function
  const parseImportFile = async (
    file: File
  ): Promise<{ data: any[]; errors: ImportValidationError[] }> => {
    try {
      let data: any[] = [];

      if (file.type === "text/csv" || file.name.endsWith(".csv")) {
        // Parse CSV
        const text = await file.text();
        const lines = text.split("\n").filter((line) => line.trim());

        if (lines.length < 2) {
          throw new Error(
            "CSV file must contain at least a header row and one data row"
          );
        }

        const headers = lines[0]
          .split(",")
          .map((h) => h.trim().replace(/"/g, ""));

        for (let i = 1; i < lines.length; i++) {
          const values = lines[i]
            .split(",")
            .map((v) => v.trim().replace(/"/g, ""));
          const row: any = {};

          headers.forEach((header, index) => {
            row[header] = values[index] || "";
          });

          data.push(row);
        }
      } else if (
        file.type ===
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        file.name.endsWith(".xlsx")
      ) {
        // Parse Excel
        const buffer = await file.arrayBuffer();
        const workbook = XLSX.read(buffer, { type: "array" });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        data = XLSX.utils.sheet_to_json(worksheet);
      } else {
        throw new Error(
          "Unsupported file format. Please use CSV or Excel (.xlsx) files."
        );
      }

      // Fetch required data for validation

      const [suppliersResponse, productsServicesResponse, categoriesResponse] =
        await Promise.all([
          fetch("/admin/supplier-management/suppliers?limit=100", {
            credentials: "include",
          }),
          fetch("/admin/supplier-management/products-services?limit=100", {
            credentials: "include",
          }),
          fetch(
            "/admin/supplier-management/products-services/categories?limit=100",
            { credentials: "include" }
          ),
        ]);

      const [suppliersData, productsServicesData, categoriesData] =
        await Promise.all([
          suppliersResponse.ok ? suppliersResponse.json() : { suppliers: [] },
          productsServicesResponse.ok
            ? productsServicesResponse.json()
            : { product_services: [] },
          categoriesResponse.ok
            ? categoriesResponse.json()
            : { categories: [] },
        ]);

      const suppliers = suppliersData.suppliers || [];
      const productsServices = productsServicesData.product_services || [];
      const categories = categoriesData.categories || [];

      // Validate the parsed data
      const errors = await validateSupplierOfferingImportData(
        data,
        suppliers,
        productsServices,
        categories
      );

      return { data, errors };
    } catch (error) {
      return {
        data: [],
        errors: [
          {
            row: 0,
            field: "file",
            message:
              error instanceof Error ? error.message : "Failed to parse file",
            value: file.name,
          },
        ],
      };
    }
  };

  // Transform data for import (convert human-readable names to IDs)
  const transformImportData = async (data: any[]): Promise<any[]> => {
    try {
      // Fetch required data for transformation
      const [suppliersResponse, productsServicesResponse, categoriesResponse] =
        await Promise.all([
          fetch("/admin/supplier-management/suppliers?limit=100", {
            credentials: "include",
          }),
          fetch("/admin/supplier-management/products-services?limit=100", {
            credentials: "include",
          }),
          fetch(
            "/admin/supplier-management/products-services/categories?limit=100",
            {
              credentials: "include",
            }
          ),
        ]);

      const [suppliersData, productsServicesData, categoriesData] =
        await Promise.all([
          suppliersResponse.json(),
          productsServicesResponse.json(),
          categoriesResponse.json(),
        ]);

      const suppliers = suppliersData.suppliers || [];
      const productsServices = productsServicesData.product_services || [];
      const categories = categoriesData.categories || [];

      const transformedData = data.map((row) => {
        const transformed: any = {
          status: row.status || "active",
          active_from: parseExcelDate(row.active_from),
          active_to: parseExcelDate(row.active_to),
          availability_notes: row.availability_notes || undefined,
        };


        // Map supplier name to ID and handle currency auto-population
        let supplier: any = null;
        if (row.supplier_name) {
          supplier = suppliers.find((s: any) => s.name === row.supplier_name);
          transformed.supplier_id = supplier?.id || row.supplier_id;
        } else {
          transformed.supplier_id = row.supplier_id;
          supplier = suppliers.find((s: any) => s.id === row.supplier_id);
        }

        // Handle pricing fields matching detail page (currency auto-populated from supplier)
        const pricingFields = [
          'gross_price',                   // Gross Price
          'commission',                    // Commission (%)
          'net_cost',                     // Net Cost
          'margin_rate',                  // Margin Rate (%)
          'selling_price',                // Selling Price
          'selling_currency',             // Selling Currency
          'exchange_rate',                // Exchange Rate
          'selling_price_selling_currency' // Selling Price (Selling Currency)
          // NOTE: 'currency' auto-populated from supplier.default_currency
        ];

        pricingFields.forEach(field => {
          if (row[field] !== undefined && row[field] !== null && row[field] !== "") {
            const value = typeof row[field] === "string" ? row[field].trim() : row[field];
            if (value !== "") {
              // Handle selling_currency as string
              if (field === 'selling_currency') {
                transformed[field] = value.toUpperCase(); // Ensure currency codes are uppercase
              } else {
                // Handle numeric pricing fields
                const parsedValue = parseFloat(value);

                if (!isNaN(parsedValue) && parsedValue >= 0) {
                  // Handle percentage fields (convert to decimal for backend)
                  if (field === 'commission' || field === 'margin_rate') {
                    transformed[field] = parsedValue / 100; // Convert percentage to decimal
                  } else {
                    // Store all other pricing values exactly as provided (no calculations)
                    transformed[field] = parsedValue;
                  }
                } else {
                  console.log(`Transform Debug - Invalid value for '${field}':`, parsedValue);
                }
              }
            } else {
              console.log(`Transform Debug - Empty value for '${field}':`, value);
            }
          } else {
            console.log(`Transform Debug - Skipping '${field}' - undefined/null/empty:`, row[field]);
          }
        });

        // Auto-populate currency from supplier (currency field removed from template)
        if (supplier?.default_currency) {
          transformed.currency = supplier.default_currency;
        } else {
          transformed.currency = "CHF"; // Fallback if supplier has no default currency
        }

        // Ignore any currency field from Excel if present (should not be in template)
        if (row.currency) {
        }

        // Handle selling currency from Excel template
        if (row.selling_currency && row.selling_currency.trim()) {
          transformed.selling_currency = row.selling_currency.trim().toUpperCase();
        } else {
          transformed.selling_currency = transformed.currency; // Default to same as cost currency
        }
        if (row.product_service_name) {
          const productService = productsServices.find(
            (ps: any) => ps.name === row.product_service_name
          );
          if (productService) {
          }
          transformed.product_service_id =
            productService?.id || row.product_service_id;
        } else {
          transformed.product_service_id = row.product_service_id;
        }

        // Handle status
        if (row.status) {
          transformed.status =
            row.status.toLowerCase() === "active" ? "active" : "inactive";
        } else {
          transformed.status = "active"; // Default to active if not specified
        }

        // Handle custom fields - process editable fields and inherit locked fields
        const customFields: Record<string, any> = {};

        // Get the product/service to find its category and locked fields
        const productService = productsServices.find(
          (ps: any) =>
            ps.id === transformed.product_service_id ||
            ps.name === row.product_service_name
        );

        let lockedFieldKeys: string[] = [];
        let editableFieldKeys: string[] = [];
        let allSupplierOfferingFields: any[] = [];

        if (productService?.category_id) {
          const category = categories.find(
            (c: any) => c.id === productService.category_id
          );
          if (category?.dynamic_field_schema) {
            // Filter fields that are used in supplier offerings, then separate locked and editable fields
            const supplierOfferingFields = category.dynamic_field_schema.filter(
              (field: any) => field.used_in_supplier_offering === true
            );

            allSupplierOfferingFields = supplierOfferingFields;

            supplierOfferingFields.forEach((field: any) => {
              if (field.locked_in_offerings === true) {
                lockedFieldKeys.push(field.key);
              } else {
                editableFieldKeys.push(field.key);
              }
            });
          }
        }

        allSupplierOfferingFields.forEach((field: any) => {
          if (field.locked_in_offerings === true) {
            // Inherit locked fields from product service
            if (productService?.custom_fields?.[field.key] !== undefined) {
              customFields[field.key] = productService.custom_fields[field.key];
            }
          } else {
            // Initialize editable fields with default values (will be overridden by Excel data if present)
            customFields[field.key] = field.default_value || "";
          }
        });

        // Process editable custom fields from Excel to override initialized values
        Object.keys(row).forEach((key) => {
          // Handle both prefixed and non-prefixed custom field keys
          let fieldKey = key;
          if (key.startsWith("custom_field_")) {
            fieldKey = key.replace("custom_field_", "");
          }

          // Skip locked fields from Excel - they are already inherited above
          if (lockedFieldKeys.includes(fieldKey)) {
            return;
          }

          // Only process editable custom fields that exist in the schema
          if (
            editableFieldKeys.includes(fieldKey) ||
            key.startsWith("custom_field_")
          ) {
            let value = row[key];

            // Find the field schema to determine type
            const fieldSchema = productService?.category_id
              ? categories
                  .find((c: any) => c.id === productService.category_id)
                  ?.dynamic_field_schema?.find((f: any) => f.key === fieldKey)
              : null;

            // Only override the initialized value if Excel provides a non-empty value
            if (
              value !== null &&
              value !== undefined &&
              value !== ""
            ) {
              // Handle different field types
              if (fieldSchema) {
                switch (fieldSchema.type) {
                  case "date":
                    // Parse date using robust date parser
                    const parsedDate = parseExcelDate(value);
                    value = parsedDate; // Will be null if parsing fails
                    break;
                  case "boolean":
                    // Convert boolean strings to actual booleans
                    if (typeof value === "string") {
                      const lowerValue = value.toLowerCase();
                      if (["true", "1", "yes"].includes(lowerValue)) {
                        value = true;
                      } else if (["false", "0", "no"].includes(lowerValue)) {
                        value = false;
                      }
                    }
                    break;
                  case "number":
                    // Ensure numbers are properly parsed
                    if (typeof value === "string") {
                      const numValue = parseFloat(value);
                      if (!isNaN(numValue)) {
                        value = numValue;
                      }
                    }
                    break;
                  // Other types (text, dropdown, multi-select) can be handled as strings
                }
              } else if (typeof value === "string") {
                // Fallback boolean conversion for fields without schema
                const lowerValue = value.toLowerCase();
                if (["true", "1", "yes"].includes(lowerValue)) {
                  value = true;
                } else if (["false", "0", "no"].includes(lowerValue)) {
                  value = false;
                }
              }

              // Override the initialized value with the Excel value
              customFields[fieldKey] = value;
            }
            // If Excel value is empty, keep the initialized default value
          }
        });

        // Note: Locked fields are already inherited above during initialization

        if (Object.keys(customFields).length > 0) {
          transformed.custom_fields = customFields;
        }

        return transformed;
      });

      return transformedData;
    } catch (error) {
      throw new Error("Failed to transform import data");
    }
  };

  // Import mutation
  const importMutation = useMutation({
    mutationFn: async (supplierOfferings: any[]): Promise<ImportResult> => {
      try {
        setImportProgress({
          processed: 0,
          total: supplierOfferings.length,
          percentage: 0,
        });

        // Transform the data before sending
        const transformedData = await transformImportData(supplierOfferings);

        const response = await fetch(
          "/admin/supplier-management/supplier-offerings/import",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            credentials: "include",
            body: JSON.stringify({ supplier_offerings: transformedData }),
          }
        );

        // Handle both success (201) and partial success (207) responses
        if (!response.ok && response.status !== 207) {
          const error = await response.json();
          throw new Error(error.message || "Import failed");
        }

        const result = await response.json();

        setImportProgress({
          processed: supplierOfferings.length,
          total: supplierOfferings.length,
          percentage: 100,
        });

        return result;
      } catch (error) {
        throw new Error(
          `Import failed: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [SUPPLIER_OFFERINGS_QUERY_KEY],
      });
      // Don't show toast here - let the component handle success/error display
    },
    onError: (error: Error) => {
      // Don't show toast here - let the component handle error display
      console.error("Import mutation error:", error);
    },
  });

  return {
    parseImportFile,
    importSupplierOfferings: importMutation.mutateAsync,
    isImporting: importMutation.isPending,
    importProgress,
    generateTemplate: generateSupplierOfferingTemplate,
    validateData: validateSupplierOfferingImportData,
    exportToCSV,
    exportToExcel,
  };
};

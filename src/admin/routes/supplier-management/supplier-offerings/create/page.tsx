import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { ArrowLeft } from "@camped-ai/icons";
import { Save, X } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Textarea,
  Select,
  Label,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { useCreateSupplierOffering } from "../../../../hooks/supplier-products-services/use-supplier-offerings";
import { useSuppliers } from "../../../../hooks/vendor-management/use-suppliers";
import { useProductsServices } from "../../../../hooks/supplier-products-services/use-products-services";
import { useCategories } from "../../../../hooks/supplier-products-services/use-categories";
import DynamicFieldRenderer, {
  type DynamicFieldSchema,
} from "../../../../components/supplier-management/dynamic-field-renderer";
import PricingCalculator from "../../../../components/supplier-management/pricing-calculator";
import { useAddonLineItemsFromSelection } from "../../../../hooks/supplier-products-services/use-addon-pricing";
import { AddonLineItem } from "../../../../components/supplier-management/addon-line-item";
import TruncatedSelectItem from "../../../../components/common/TruncatedSelectItem";

interface FormData {
  supplier_id: string;
  product_service_id: string;
  active_from: string;
  active_to: string;
  availability_notes: string;

  // Legacy cost field (for backward compatibility)
  cost: string;

  // Enhanced pricing fields
  commission: string;
  gross_price: string;
  net_cost: string;
  margin_rate: string;
  selling_price: string;

  // Currency fields
  currency: string;
  currency_override: boolean;

  // Selling currency fields
  selling_currency: string;
  selling_price_selling_currency: string;
  exchange_rate: string;
  exchange_rate_date: string;

  status: "active" | "inactive";
  custom_fields: Record<string, any>;
  add_ons?: any[]; // Array of addon line items
}

const CreateSupplierOfferingPage = () => {
  const navigate = useNavigate();
  const createSupplierOffering = useCreateSupplierOffering();

  // API calls
  const { data: suppliersResponse, isLoading: suppliersLoading } =
    useSuppliers();
  const { data: productsServicesResponse, isLoading: productsServicesLoading } =
    useProductsServices();
  const { data: categoriesResponse } = useCategories();

  // Extract arrays from response objects
  const suppliers = suppliersResponse?.suppliers || [];
  const productsServices = productsServicesResponse?.product_services || [];
  const categories = categoriesResponse?.categories || [];

  const [formData, setFormData] = useState<FormData>({
    supplier_id: "",
    product_service_id: "",
    active_from: new Date().toISOString().split("T")[0], // Default to today
    active_to: "",
    availability_notes: "",

    // Legacy cost field
    cost: "",

    // Enhanced pricing fields
    commission: "",
    gross_price: "",
    net_cost: "",
    margin_rate: "",
    selling_price: "",

    // Currency fields
    currency: "CHF",
    currency_override: false,

    // Selling currency fields
    selling_currency: "CHF",
    selling_price_selling_currency: "",
    exchange_rate: "1.0",
    exchange_rate_date: "",

    status: "active",
    custom_fields: {},
    add_ons: [],
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [selectedProductService, setSelectedProductService] =
    useState<any>(null);
  const [selectedSupplier, setSelectedSupplier] = useState<any>(null);
  const [categorySchema, setCategorySchema] = useState<DynamicFieldSchema[]>(
    []
  );
  const [addonLineItems, setAddonLineItems] = useState<AddonLineItem[]>([]);

  // Hook for creating addon line items from selection
  const { createLineItemsFromSelection, isLoading: isLoadingAddonPricing } =
    useAddonLineItemsFromSelection();

  // Extract available addons from product/service schema (not from selected values)
  const getAvailableAddons = () => {
    const addonOptions: { value: string; label: string }[] = [];

    // Get addons from the selected product/service, not from custom field selections
    if (selectedProductService?.custom_fields) {
      categorySchema.forEach((field) => {
        if (field.type === "addons" && selectedProductService.custom_fields?.[field.key]) {
          const availableAddonIds = Array.isArray(selectedProductService.custom_fields[field.key])
            ? selectedProductService.custom_fields[field.key]
            : [];

          availableAddonIds.forEach((addonId: string) => {
            const addon = productsServices.find((ps) => ps.id === addonId);
            if (addon && !addonOptions.some(opt => opt.value === addonId)) {
              addonOptions.push({
                value: addonId,
                label: addon.name,
              });
            }
          });
        }
      });
    }

    return addonOptions;
  };

  // Get currently selected addon IDs from custom fields
  const getCurrentlySelectedAddons = () => {
    const selectedIds: string[] = [];

    categorySchema.forEach((field) => {
      if (field.type === "addons" && formData.custom_fields[field.key]) {
        const selectedAddonIds = Array.isArray(formData.custom_fields[field.key])
          ? formData.custom_fields[field.key]
          : [];

        selectedIds.push(...selectedAddonIds);
      }
    });

    return selectedIds;
  };

  // Update selected supplier and auto-fill currency when supplier_id changes
  useEffect(() => {
    if (formData.supplier_id) {
      const supplier = suppliers.find((s) => s.id === formData.supplier_id);
      setSelectedSupplier(supplier);

      // Auto-fill currency from supplier's default currency if not overridden
      if (supplier?.default_currency && !formData.currency_override) {
        setFormData((prev) => ({
          ...prev,
          currency: supplier.default_currency,
        }));
      }
    } else {
      setSelectedSupplier(null);
    }
  }, [formData.supplier_id, suppliers, formData.currency_override]);

  // Update selected product service and category schema when product_service_id changes
  useEffect(() => {
    if (formData.product_service_id) {
      const productService = productsServices.find(
        (ps) => ps.id === formData.product_service_id
      );
      setSelectedProductService(productService);

      if (productService?.category?.dynamic_field_schema) {
        // Filter fields that are used in supplier offerings
        const offeringFields =
          productService.category.dynamic_field_schema.filter(
            (field: any) => field.used_in_supplier_offering
          ) as DynamicFieldSchema[];
        setCategorySchema(offeringFields);

        // Initialize custom fields as empty for CREATE page
        // Users will manually select values - no auto-population
        const initialCustomFields: Record<string, any> = {};

        // Only initialize non-addon fields that are locked
        offeringFields.forEach((field: any) => {
          if (
            field.locked_in_offerings &&
            field.type !== "addons" && // Skip addon fields - let users select manually
            productService.custom_fields?.[field.key] !== undefined
          ) {
            // Inherit value from product/service for locked non-addon fields only
            initialCustomFields[field.key] =
              productService.custom_fields[field.key];
          }
        });

        setFormData((prev) => ({
          ...prev,
          custom_fields: initialCustomFields,
        }));

        console.log("✅ Custom fields initialized (addon fields left empty for manual selection)");
      } else {
        setCategorySchema([]);
      }
    } else {
      setSelectedProductService(null);
      setCategorySchema([]);
    }
  }, [formData.product_service_id, productsServices]);

  // AUTO-POPULATION DISABLED FOR CREATE PAGE
  // In create mode, users should manually select addons - no auto-population
  // This ensures addon fields appear empty and users have full control
  useEffect(() => {
    // No auto-population logic for create page
    // Users will manually select addons in custom fields
    console.log("🚫 Auto-population disabled for CREATE page - users will manually select addons");
  }, [
    selectedProductService,
    formData.product_service_id,
  ]);

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const handleCustomFieldChange = (fieldKey: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      custom_fields: {
        ...prev.custom_fields,
        [fieldKey]: value,
      },
    }));
  };

  // Handle pricing calculator changes
  const handlePricingChange = (pricingData: any) => {
    setFormData((prev) => ({
      ...prev,
      commission: pricingData.commission?.toString() || "",
      gross_price: pricingData.grossPrice?.toString() || "",
      net_cost:
        (
          pricingData.calculatedSupplierPrice || pricingData.supplierPrice
        )?.toString() || "",
      margin_rate: pricingData.marginRate?.toString() || "",
      selling_price: pricingData.calculatedSellingPrice?.toString() || "",

      // Selling currency fields
      selling_currency: pricingData.sellingCurrency || prev.selling_currency,
      selling_price_selling_currency:
        pricingData.calculatedSellingPriceSellingCurrency?.toString() || "",
      exchange_rate: pricingData.exchangeRate?.toString() || prev.exchange_rate,
      exchange_rate_date: pricingData.exchangeRateDate
        ? pricingData.exchangeRateDate.toISOString()
        : prev.exchange_rate_date,

      // Store addon line items in add_ons field
      add_ons: pricingData.addonLineItems || [],
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.supplier_id) {
      newErrors.supplier_id = "Supplier is required";
    }

    if (!formData.product_service_id) {
      newErrors.product_service_id = "Product/Service is required";
    }

    // REQUIRED PRICING FIELDS VALIDATION
    // Gross Price (required)
    if (!formData.gross_price || formData.gross_price.trim() === "") {
      newErrors.gross_price = "Gross price is required";
    } else {
      const grossPrice = parseFloat(formData.gross_price);
      if (isNaN(grossPrice) || grossPrice <= 0) {
        newErrors.gross_price = "Gross price must be a positive number";
      }
    }

    // Commission (optional)
    if (formData.commission && formData.commission.trim() !== "") {
      const commission = parseFloat(formData.commission);
      if (isNaN(commission) || commission < 0 || commission > 100) {
        newErrors.commission = "Commission must be between 0% and 100%";
      }
    }

    // Net Cost (required)
    if (!formData.net_cost || formData.net_cost.trim() === "") {
      newErrors.net_cost = "Net cost is required";
    } else {
      const netCost = parseFloat(formData.net_cost);
      if (isNaN(netCost) || netCost <= 0) {
        newErrors.net_cost = "Net cost must be a positive number";
      }
    }

    // Margin Rate (required)
    if (!formData.margin_rate || formData.margin_rate.trim() === "") {
      newErrors.margin_rate = "Margin rate is required";
    } else {
      const marginRate = parseFloat(formData.margin_rate);
      if (isNaN(marginRate) || marginRate < 0 || marginRate > 99.99) {
        newErrors.margin_rate = "Margin rate must be between 0% and 99.99%";
      }
    }

    // Selling Price (required)
    if (!formData.selling_price || formData.selling_price.trim() === "") {
      newErrors.selling_price = "Selling price is required";
    } else {
      const sellingPrice = parseFloat(formData.selling_price);
      if (isNaN(sellingPrice) || sellingPrice <= 0) {
        newErrors.selling_price = "Selling price must be a positive number";
      }
    }

    // Selling Currency (required)
    if (!formData.selling_currency || formData.selling_currency.trim() === "") {
      newErrors.selling_currency = "Selling currency is required";
    } else if (!/^[A-Z]{3}$/.test(formData.selling_currency)) {
      newErrors.selling_currency =
        "Selling currency must be a valid 3-letter code";
    }

    // Exchange Rate (required)
    if (!formData.exchange_rate || formData.exchange_rate.trim() === "") {
      newErrors.exchange_rate = "Exchange rate is required";
    } else {
      const exchangeRate = parseFloat(formData.exchange_rate);
      if (isNaN(exchangeRate) || exchangeRate <= 0) {
        newErrors.exchange_rate = "Exchange rate must be a positive number";
      }
    }

    // Selling Price (Selling Currency) (required)
    if (
      !formData.selling_price_selling_currency ||
      formData.selling_price_selling_currency.trim() === ""
    ) {
      newErrors.selling_price_selling_currency =
        "Selling price (selling currency) is required";
    } else {
      const sellingPriceSellingCurrency = parseFloat(
        formData.selling_price_selling_currency
      );
      if (
        isNaN(sellingPriceSellingCurrency) ||
        sellingPriceSellingCurrency <= 0
      ) {
        newErrors.selling_price_selling_currency =
          "Selling price (selling currency) must be a positive number";
      }
    }

    if (!formData.currency) {
      newErrors.currency = "Currency is required";
    } else if (!/^[A-Z]{3}$/.test(formData.currency)) {
      newErrors.currency =
        "Currency must be a valid 3-letter code (e.g., CHF, EUR, USD)";
    }

    // Validate date range
    if (formData.active_from && formData.active_to) {
      const fromDate = new Date(formData.active_from);
      const toDate = new Date(formData.active_to);

      if (fromDate >= toDate) {
        newErrors.active_to = "Active To date must be after Active From date";
      }
    }

    // Validate required custom fields (skip locked fields as they're inherited)
    categorySchema.forEach((field) => {
      if (
        field.required &&
        !field.locked_in_offerings &&
        !formData.custom_fields[field.key]
      ) {
        newErrors[`custom_field_${field.key}`] = `${field.label} is required`;
      }
    });
    console.log("newErrors", newErrors);

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("Please fill the required fields before submitting");
      return;
    }

    try {
      const submitData = {
        ...formData,
        active_from: formData.active_from || null,
        active_to: formData.active_to || null,
        availability_notes: formData.availability_notes || undefined,

        // Legacy cost field
        cost: formData.cost ? parseFloat(formData.cost) : undefined,

        // Enhanced pricing fields
        commission: formData.commission
          ? parseFloat(formData.commission)
          : undefined,
        gross_price: formData.gross_price
          ? parseFloat(formData.gross_price)
          : undefined,
        net_cost: formData.net_cost ? parseFloat(formData.net_cost) : undefined,
        margin_rate: formData.margin_rate
          ? parseFloat(formData.margin_rate)
          : undefined,
        selling_price: formData.selling_price
          ? parseFloat(formData.selling_price)
          : undefined,

        // Currency fields
        currency: formData.currency,
        currency_override: formData.currency_override,

        // Selling currency fields
        selling_currency: formData.selling_currency,
        selling_price_selling_currency: formData.selling_price_selling_currency
          ? parseFloat(formData.selling_price_selling_currency)
          : undefined,
        exchange_rate: formData.exchange_rate
          ? parseFloat(formData.exchange_rate)
          : undefined,
        exchange_rate_date: formData.exchange_rate_date
          ? new Date(formData.exchange_rate_date).toISOString()
          : undefined,
      };

      await createSupplierOffering.mutateAsync(submitData);
      navigate("/supplier-management/supplier-offerings");
    } catch (error: any) {
      console.error("Error creating supplier offering:", error);

      // Handle specific error types
      if (error?.response?.status === 409) {
        // Handle validation errors from the server
        let errorMessage =
          "This supplier offering configuration already exists.";

        if (error?.response?.data?.message) {
          errorMessage = error.response.data.message;
        } else if (error?.message) {
          errorMessage = error.message;
        }

        // Provide specific guidance for different types of conflicts
        if (errorMessage.includes("Date range conflict")) {
          // Already has specific date information
        } else if (errorMessage.includes("open-ended validity")) {
          // Already has specific open-ended conflict information
        } else if (errorMessage.includes("Cannot create offering")) {
          // Already has specific creation conflict information
        }

        toast.error(errorMessage);
      } else if (error?.response?.status === 400) {
        // Handle validation errors
        let errorMessage = "Invalid data provided.";

        if (error?.response?.data?.message) {
          errorMessage = error.response.data.message;
        } else if (error?.message) {
          errorMessage = error.message;
        }

        toast.error(errorMessage);
      } else {
        toast.error(error?.message || "Failed to create supplier offering");
      }
    }
  };

  const handleCancel = () => {
    navigate("/supplier-management/supplier-offerings");
  };

  // Handle addon selection changes from custom fields
  const handleAddonSelectionChange = async (
    fieldKey: string,
    selectedAddonIds: string[]
  ) => {
    console.log("🎯 Addon selection changed:", { fieldKey, selectedAddonIds });
    try {
      // Update custom fields with selected addon IDs
      setFormData((prev) => ({
        ...prev,
        custom_fields: {
          ...prev.custom_fields,
          [fieldKey]: selectedAddonIds,
        },
      }));

      // Create line items from selection with pricing lookup
      const lineItems = await createLineItemsFromSelection(
        selectedAddonIds,
        {
          supplier_id: formData.supplier_id,
          activity_start_date: formData.active_from,
          activity_end_date: formData.active_to,
        },
        productsServices
      );

      // Ensure supplier name is populated from the currently selected supplier
      const enhancedLineItems = lineItems.map(item => ({
        ...item,
        supplier_name: item.supplier_name || selectedSupplier?.name || 'Unknown Supplier'
      }));

      console.log("💾 Setting addon line items with supplier names:", enhancedLineItems);
      setAddonLineItems(enhancedLineItems);
    } catch (error) {
      console.error("Error handling addon selection change:", error);
      toast.error("Failed to load addon pricing");
    }
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-x-4">
            <Button
              variant="transparent"
              onClick={handleCancel}
              className="p-1"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <Heading level="h2">Add New Supplier Offering</Heading>
              <Text className="text-ui-fg-subtle">
                Define which supplier offers which product/service with custom
                configuration
              </Text>
            </div>
          </div>
          <div className="flex items-center gap-x-2">
            <Button variant="secondary" onClick={handleCancel}>
              <X className="h-4 w-4" />
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              isLoading={createSupplierOffering.isPending}
            >
              <Save className="h-4 w-4" />
              Save Offering
            </Button>
          </div>
        </div>

        {/* Form */}
        <div className="px-6 py-6">
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Basic Information */}
            <div className="space-y-6">
              <div>
                <Heading level="h3">Basic Information</Heading>
                <Text className="text-ui-fg-subtle">
                  Select the supplier and product/service for this offering. The
                  category will be auto-filled and custom fields will load based
                  on your selection.
                </Text>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="supplier_id">
                    Supplier <span className="text-ui-fg-error">*</span>
                  </Label>
                  <Select
                    value={formData.supplier_id || ""}
                    onValueChange={(value) =>
                      handleInputChange("supplier_id", value)
                    }
                  >
                    <Select.Trigger>
                      <Select.Value placeholder="Select a supplier" />
                    </Select.Trigger>
                    <Select.Content>
                      {suppliersLoading ? (
                        <div className="p-2 text-ui-fg-subtle text-sm">
                          Loading suppliers...
                        </div>
                      ) : suppliers.length === 0 ? (
                        <div className="p-2 text-ui-fg-subtle text-sm">
                          No suppliers available
                        </div>
                      ) : (
                        suppliers.map((supplier: any) => (
                          <Select.Item key={supplier.id} value={supplier.id}>
                            <TruncatedSelectItem
                              text = {supplier.name}
                              maxLength={50}
                            />
                          </Select.Item>
                        ))
                      )}
                    </Select.Content>
                  </Select>
                  {errors.supplier_id && (
                    <Text size="small" className="text-ui-fg-error">
                      {errors.supplier_id}Product/Service 
                    </Text>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="product_service_id">
                    Product/Service <span className="text-ui-fg-error">*</span>
                  </Label>
                  <Select
                    value={formData.product_service_id || ""}
                    onValueChange={(value) =>
                      handleInputChange("product_service_id", value)
                    }
                  >
                    <Select.Trigger>
                      <Select.Value placeholder="Select a product/service" />
                    </Select.Trigger>
                    <Select.Content>
                      {productsServicesLoading ? (
                        <div className="p-2 text-ui-fg-subtle text-sm">
                          Loading products/services...
                        </div>
                      ) : productsServices.length === 0 ? (
                        <div className="p-2 text-ui-fg-subtle text-sm">
                          No products/services available
                        </div>
                      ) : (
                        productsServices.map((ps: any) => (
                          <Select.Item key={ps.id} value={ps.id}>
                            <TruncatedSelectItem
                              text={`${ps.name} (${ps.type})`}
                              maxLength={70}
                            />
                          </Select.Item>
                        ))
                      )}
                    </Select.Content>
                  </Select>
                  {errors.product_service_id && (
                    <Text size="small" className="text-ui-fg-error">
                      {errors.product_service_id}
                    </Text>
                  )}
                </div>
              </div>

              {/* Show category info when product/service is selected */}
              {selectedProductService && (
                <div className="space-y-2">
                  <Label>Category (Auto-filled)</Label>
                  <div className="p-3 bg-ui-bg-subtle rounded-lg border-l-4 border-ui-border-interactive">
                    <Text weight="plus">
                      {selectedProductService.category?.name ||
                        "No category assigned"}
                    </Text>
                    <Text size="small" className="text-ui-fg-subtle">
                      Automatically linked from selected product/service
                    </Text>
                  </div>
                </div>
              )}
            </div>

            {/* Validity Period */}
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="active_from">Active From</Label>
                  <Input
                    id="active_from"
                    type="date"
                    value={formData.active_from}
                    onChange={(e) =>
                      handleInputChange("active_from", e.target.value)
                    }
                  />
                  {errors.active_from && (
                    <Text size="small" className="text-ui-fg-error">
                      {errors.active_from}
                    </Text>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="active_to">Active To</Label>
                  <Input
                    id="active_to"
                    type="date"
                    value={formData.active_to}
                    onChange={(e) =>
                      handleInputChange("active_to", e.target.value)
                    }
                  />
                  {errors.active_to && (
                    <Text size="small" className="text-ui-fg-error">
                      {errors.active_to}
                    </Text>
                  )}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Status</Label>
              <div className="flex gap-6">
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="radio"
                    name="status"
                    value="active"
                    checked={formData.status === "active"}
                    onChange={(e) =>
                      handleInputChange("status", e.target.value)
                    }
                    className="w-4 h-4 text-ui-fg-interactive border-ui-border-base focus:ring-ui-border-interactive"
                  />
                  <Text size="small">Active</Text>
                </label>
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="radio"
                    name="status"
                    value="inactive"
                    checked={formData.status === "inactive"}
                    onChange={(e) =>
                      handleInputChange("status", e.target.value)
                    }
                    className="w-4 h-4 text-ui-fg-interactive border-ui-border-base focus:ring-ui-border-interactive"
                  />
                  <Text size="small">Inactive</Text>
                </label>
              </div>
            </div>

            {/* Currency Selection */}
            <div className="">
              <div className="space-y-2">
                <Label htmlFor="currency">
                  Currency <span className="text-ui-fg-error">*</span>
                </Label>
                <div className="space-y-3">
                  {selectedSupplier?.default_currency &&
                  !formData.currency_override ? (
                    <div className="space-y-3">
                      <div className="relative">
                        <Input
                          id="currency"
                          value={selectedSupplier.default_currency}
                          readOnly
                          className="bg-ui-bg-subtle cursor-not-allowed"
                          placeholder="Currency will be set from supplier"
                        />
                        <Text size="small" className="text-ui-fg-subtle mt-1">
                          Using supplier's default currency
                        </Text>
                      </div>
                      {/* <Button
                        type="button"
                        variant="secondary"
                        size="small"
                        onClick={() =>
                          handleInputChange("currency_override", true)
                        }
                        className="w-full"
                      >
                        Change Currency
                      </Button> */}
                    </div>
                  ) : !selectedSupplier ? (
                    <div className="relative">
                      <Input
                        id="currency"
                        value=""
                        readOnly
                        className="bg-ui-bg-subtle cursor-not-allowed"
                        placeholder="Select a supplier first"
                      />
                      <Text size="small" className="text-ui-fg-subtle mt-1">
                        Currency will be set from the supplier's default
                        currency
                      </Text>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Select
                        value={formData.currency}
                        onValueChange={(value) =>
                          handleInputChange("currency", value)
                        }
                      >
                        <Select.Trigger>
                          <Select.Value placeholder="Select currency" />
                        </Select.Trigger>
                        <Select.Content>
                          <Select.Item value="CHF">
                            CHF - Swiss Franc
                          </Select.Item>
                          <Select.Item value="EUR">EUR - Euro</Select.Item>
                          <Select.Item value="USD">USD - US Dollar</Select.Item>
                          <Select.Item value="GBP">
                            GBP - British Pound
                          </Select.Item>
                          <Select.Item value="CAD">
                            CAD - Canadian Dollar
                          </Select.Item>
                          <Select.Item value="AUD">
                            AUD - Australian Dollar
                          </Select.Item>
                          <Select.Item value="JPY">
                            JPY - Japanese Yen
                          </Select.Item>
                        </Select.Content>
                      </Select>

                      {selectedSupplier?.default_currency && (
                        <div className="flex items-center justify-between p-2 bg-blue-50 border border-blue-200 rounded">
                          <Text size="small" className="text-blue-700">
                            🔄 Currency override is active
                          </Text>
                          <button
                            type="button"
                            onClick={() => {
                              handleInputChange("currency_override", false);
                              handleInputChange(
                                "currency",
                                selectedSupplier.default_currency
                              );
                            }}
                            className="text-xs text-blue-600 hover:text-blue-800 font-medium underline"
                          >
                            Use Default ({selectedSupplier.default_currency})
                          </button>
                        </div>
                      )}
                    </div>
                  )}

                  {selectedSupplier?.default_currency &&
                    formData.currency_override &&
                    formData.currency !== selectedSupplier.default_currency && (
                      <div className="flex items-start gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded">
                        <Text size="small" className="text-yellow-700">
                          ⚠️ <strong>Currency Mismatch:</strong> You selected{" "}
                          {formData.currency} but the supplier's default is{" "}
                          {selectedSupplier.default_currency}. This may affect
                          pricing calculations and financial reporting.
                        </Text>
                      </div>
                    )}

                  {errors.currency && (
                    <Text size="small" className="text-ui-fg-error">
                      {errors.currency}
                    </Text>
                  )}
                </div>
              </div>
            </div>

            {/* Enhanced Pricing */}
            <div className="space-y-6">
              <PricingCalculator
                initialData={{
                  commission: formData.commission
                    ? parseFloat(formData.commission)
                    : undefined,
                  grossPrice: formData.gross_price
                    ? parseFloat(formData.gross_price)
                    : undefined,
                  supplierPrice: formData.net_cost
                    ? parseFloat(formData.net_cost)
                    : undefined,
                  marginRate: formData.margin_rate
                    ? parseFloat(formData.margin_rate)
                    : undefined,
                  sellingPrice: formData.selling_price
                    ? parseFloat(formData.selling_price)
                    : undefined,

                  // Currency fields
                  currency: formData.currency,

                  // Selling currency fields
                  sellingCurrency: formData.selling_currency,
                  sellingPriceSellingCurrency:
                    formData.selling_price_selling_currency
                      ? parseFloat(formData.selling_price_selling_currency)
                      : undefined,
                  exchangeRate: formData.exchange_rate
                    ? parseFloat(formData.exchange_rate)
                    : undefined,
                  exchangeRateDate: formData.exchange_rate_date
                    ? new Date(formData.exchange_rate_date)
                    : undefined,
                  addonLineItems: addonLineItems,
                }}
                onChange={handlePricingChange}
                costCurrency={formData.currency}
                errors={errors}
                showValidation={true}
                defaultSupplierId={formData.supplier_id}
                availableAddons={getAvailableAddons()}
                currentlySelectedAddons={getCurrentlySelectedAddons()}
                onAddonSelectionChange={(addonIds) => {
                  // Handle addon selection from the "Add Rows" button
                  if (addonIds.length > 0) {
                    // Find the first addon field to trigger the selection change
                    const addonField = categorySchema.find(field => field.type === "addons");
                    if (addonField) {
                      handleAddonSelectionChange(addonField.key, addonIds);
                    }
                  }
                }}
              />
            </div>

            {/* Availability */}
            <div className="space-y-6">
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="availability_notes">Availability Notes</Label>
                  <Textarea
                    id="availability_notes"
                    placeholder="Optional notes about availability, restrictions, or special conditions..."
                    value={formData.availability_notes}
                    onChange={(e) =>
                      handleInputChange("availability_notes", e.target.value)
                    }
                    rows={3}
                  />
                </div>
              </div>
            </div>

            {/* Dynamic Custom Fields */}
            {categorySchema.length > 0 && (
              <div className="space-y-6">
                <DynamicFieldRenderer
                  schema={categorySchema}
                  values={formData.custom_fields}
                  onChange={handleCustomFieldChange}
                  errors={errors}
                  inheritedValues={selectedProductService?.custom_fields || {}}
                  showInheritanceIndicators={true}
                  fieldContext="supplier"
                  onAddonSelectionChange={handleAddonSelectionChange}
                  excludeProductServiceId={formData.product_service_id}
                />
              </div>
            )}
          </form>
        </div>
      </Container>
      <Toaster />
    </>
  );
};

export const config = defineRouteConfig({
  label: "Add Supplier Offering",
});

export default CreateSupplierOfferingPage;

import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { ArrowLeft } from "@camped-ai/icons";
import { Save, X } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Textarea,
  Select,
  Label,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";
import {
  useSupplierOffering,
  useUpdateSupplierOffering,
} from "../../../../../hooks/supplier-products-services/use-supplier-offerings";
import { useSuppliers } from "../../../../../hooks/vendor-management/use-suppliers";
import { useProductsServices } from "../../../../../hooks/supplier-products-services/use-products-services";
import { useCategories } from "../../../../../hooks/supplier-products-services/use-categories";
import Dynamic<PERSON>ieldRenderer, {
  type DynamicFieldSchema,
} from "../../../../../components/supplier-management/dynamic-field-renderer";
import PricingCalculator from "../../../../../components/supplier-management/pricing-calculator";
import { useAddonLineItemsFromSelection } from "../../../../../hooks/supplier-products-services/use-addon-pricing";
import { AddonLineItem } from "../../../../../components/supplier-management/addon-line-item";
import TruncatedSelectItem from "../../../../../components/common/TruncatedSelectItem";

interface FormData {
  supplier_id: string;
  product_service_id: string;
  active_from: string;
  active_to: string;
  availability_notes: string;

  // Legacy cost field
  cost: string;

  // Enhanced pricing fields
  commission: string;
  gross_price: string;
  net_cost: string;
  margin_rate: string;
  selling_price: string;

  // Currency fields
  currency: string;
  currency_override: boolean;

  // Selling currency fields
  selling_currency: string;
  selling_price_selling_currency: string;
  exchange_rate: string;
  exchange_rate_date: string;

  status: "active" | "inactive";
  custom_fields: Record<string, any>;
  add_ons?: any[]; // Array of addon line items
}

const EditSupplierOfferingPage = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  // API calls
  const {
    data: offeringResponse,
    isLoading: offeringLoading,
    error,
  } = useSupplierOffering(id!);

  const { data: suppliersResponse, isLoading: suppliersLoading } =
    useSuppliers();
  const { data: productsServicesResponse, isLoading: productsServicesLoading } =
    useProductsServices();
  const { data: categoriesResponse } = useCategories();

  const updateSupplierOffering = useUpdateSupplierOffering();

  // Extract data
  const offering = offeringResponse?.supplier_offering;
  const suppliers = suppliersResponse?.suppliers || [];
  const productsServices = productsServicesResponse?.product_services || [];

  const [formData, setFormData] = useState<FormData>({
    supplier_id: "",
    product_service_id: "",
    active_from: "",
    active_to: "",
    availability_notes: "",

    // Legacy cost field
    cost: "",

    // Enhanced pricing fields
    commission: "",
    gross_price: "",
    net_cost: "",
    margin_rate: "",
    selling_price: "",

    // Currency fields
    currency: "CHF",
    currency_override: false,

    // Selling currency fields
    selling_currency: "CHF",
    selling_price_selling_currency: "",
    exchange_rate: "1.0",
    exchange_rate_date: "",

    status: "active",
    custom_fields: {},
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [selectedProductService, setSelectedProductService] =
    useState<any>(null);
  const [selectedSupplier, setSelectedSupplier] = useState<any>(null);
  const [categorySchema, setCategorySchema] = useState<DynamicFieldSchema[]>(
    []
  );
  const [addonLineItems, setAddonLineItems] = useState<AddonLineItem[]>([]);
  const [isInitializing, setIsInitializing] = useState(true);

  // Hook for creating addon line items from selection
  const { createLineItemsFromSelection } = useAddonLineItemsFromSelection();

  // Extract available addons from product/service schema (not from selected values)
  const getAvailableAddons = () => {
    const addonOptions: { value: string; label: string }[] = [];

    // Get addons from the selected product/service, not from custom field selections
    if (selectedProductService?.custom_fields) {
      categorySchema.forEach((field) => {
        if (field.type === "addons" && selectedProductService.custom_fields?.[field.key]) {
          const availableAddonIds = Array.isArray(selectedProductService.custom_fields[field.key])
            ? selectedProductService.custom_fields[field.key]
            : [];

          availableAddonIds.forEach((addonId: string) => {
            const addon = productsServices.find((ps) => ps.id === addonId);
            if (addon && !addonOptions.some(opt => opt.value === addonId)) {
              addonOptions.push({
                value: addonId,
                label: addon.name,
              });
            }
          });
        }
      });
    }

    return addonOptions;
  };

  // Get currently selected addon IDs from custom fields
  const getCurrentlySelectedAddons = () => {
    const selectedIds: string[] = [];

    categorySchema.forEach((field) => {
      if (field.type === "addons" && formData.custom_fields[field.key]) {
        const selectedAddonIds = Array.isArray(formData.custom_fields[field.key])
          ? formData.custom_fields[field.key]
          : [];

        selectedIds.push(...selectedAddonIds);
      }
    });

    return selectedIds;
  };

  // Initialize form data when offering loads
  useEffect(() => {
    if (offering) {
      // Determine the currency to use based on override flag
      let currencyToUse = offering.currency || "CHF";
      const isOverride = offering.currency_override || false;

      // If not overridden and supplier has default currency, use supplier's default
      if (!isOverride && offering.supplier?.default_currency) {
        currencyToUse = offering.supplier.default_currency;
      }

      setFormData({
        supplier_id: offering.supplier_id,
        product_service_id: offering.product_service_id,
        active_from: offering.active_from
          ? offering.active_from.split("T")[0]
          : "",
        active_to: offering.active_to ? offering.active_to.split("T")[0] : "",
        availability_notes: offering.availability_notes || "",

        // Legacy cost field
        cost: offering.cost ? offering.cost.toString() : "",

        // Enhanced pricing fields (API returns string percentages like "11.00")
        commission: offering.commission ? offering.commission.toString() : "",
        gross_price: offering.gross_price
          ? offering.gross_price.toString()
          : "",
        net_cost: offering.net_cost ? offering.net_cost.toString() : "",
        margin_rate: offering.margin_rate
          ? offering.margin_rate.toString()
          : "",
        selling_price: offering.selling_price
          ? offering.selling_price.toString()
          : "",

        // Currency fields
        currency: currencyToUse,
        currency_override: isOverride,

        // Selling currency fields
        selling_currency: offering.selling_currency || currencyToUse,
        selling_price_selling_currency: offering.selling_price_selling_currency
          ? offering.selling_price_selling_currency.toString()
          : "",
        exchange_rate: offering.exchange_rate
          ? offering.exchange_rate.toString()
          : "1.0",
        exchange_rate_date: offering.exchange_rate_date
          ? offering.exchange_rate_date.toISOString().split("T")[0]
          : "",

        status: offering.status,
        custom_fields: offering.custom_fields || {},
        add_ons: offering.add_ons || [],
      });

      // Load addon line items from add_ons field
      if (offering.add_ons) {
        setAddonLineItems(offering.add_ons);
      }

      // Set selected supplier and product service
      if (offering.supplier) {
        setSelectedSupplier(offering.supplier);
      }
      if (offering.product_service) {
        setSelectedProductService(offering.product_service);
      }

      // Mark initialization as complete
      setIsInitializing(false);

      // Set category schema if available
      if (offering.product_service?.category?.dynamic_field_schema) {
        const offeringFields =
          offering.product_service.category.dynamic_field_schema.filter(
            (field: any) => field.used_in_supplier_offering
          );
        setCategorySchema(offeringFields);

        // Ensure locked fields have inherited values
        const updatedCustomFields = { ...offering.custom_fields };
        offeringFields.forEach((field: any) => {
          if (
            field.locked_in_offerings &&
            offering.product_service?.custom_fields?.[field.key] !== undefined
          ) {
            updatedCustomFields[field.key] =
              offering.product_service.custom_fields[field.key];
          }
        });

        // Update form data if there are any locked field updates
        if (
          JSON.stringify(updatedCustomFields) !==
          JSON.stringify(offering.custom_fields)
        ) {
          setFormData((prev) => ({
            ...prev,
            custom_fields: updatedCustomFields,
          }));
        }
      }
    }
  }, [offering]);

  // Update selected supplier and auto-fill currency when supplier_id changes
  useEffect(() => {
    if (formData.supplier_id) {
      const supplier = suppliers.find((s) => s.id === formData.supplier_id);
      setSelectedSupplier(supplier);

      // Auto-fill currency from supplier's default currency if not overridden
      if (supplier?.default_currency && !formData.currency_override) {
        setFormData((prev) => ({
          ...prev,
          currency: supplier.default_currency,
        }));
      }
    } else {
      setSelectedSupplier(null);
    }
  }, [formData.supplier_id, suppliers, formData.currency_override]);

  // Update selected product service and category schema when product_service_id changes
  useEffect(() => {
    if (formData.product_service_id) {
      const productService = productsServices.find(
        (ps) => ps.id === formData.product_service_id
      );
      setSelectedProductService(productService);

      if (productService?.category?.dynamic_field_schema) {
        // Filter fields that are used in supplier offerings
        const offeringFields =
          productService.category.dynamic_field_schema.filter(
            (field: any) => field.used_in_supplier_offering
          ) as DynamicFieldSchema[];
        setCategorySchema(offeringFields);

        // Initialize custom fields with default values and inherit locked non-addon fields only
        const newCustomFields: Record<string, any> = {};

        offeringFields.forEach((field: any) => {
          if (
            field.locked_in_offerings &&
            field.type !== "addons" && // Skip addon fields - preserve only saved values
            productService.custom_fields?.[field.key] !== undefined
          ) {
            // Inherit value from product service for locked non-addon fields only
            newCustomFields[field.key] =
              productService.custom_fields[field.key];
          } else if (formData.custom_fields[field.key] === undefined) {
            // Set default value for new fields
            newCustomFields[field.key] = field.default_value || "";
          } else {
            // Keep existing value
            newCustomFields[field.key] = formData.custom_fields[field.key];
          }
        });

        setFormData((prev) => ({
          ...prev,
          custom_fields: { ...prev.custom_fields, ...newCustomFields },
        }));

        // Auto-update locked fields to inherit from currently selected Product/Service
        offeringFields.forEach((field: any) => {
          if (field.locked_in_offerings && productService.custom_fields?.[field.key] !== undefined) {
            const currentValue = productService.custom_fields[field.key];
            newCustomFields[field.key] = currentValue;
          }
        });
      } else {
        setCategorySchema([]);
      }
    } else {
      setSelectedProductService(null);
      setCategorySchema([]);
    }
  }, [formData.product_service_id, productsServices]);

  // AUTO-POPULATION DISABLED FOR EDIT PAGE
  // In edit mode, users should only see previously saved addon selections
  // No auto-population should occur - only show actual saved values
  useEffect(() => {
    // No auto-population logic for edit page
    // Users will see only their previously saved addon selections
  }, [
    selectedProductService,
    formData.product_service_id,
  ]);

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  // Handle pricing calculator changes
  const handlePricingChange = (pricingData: any) => {
    // Don't update formData during initialization to prevent infinite loops
    if (isInitializing) {
      return;
    }

    setFormData((prev) => ({
      ...prev,
      commission: pricingData.commission?.toString() || "",
      gross_price: pricingData.grossPrice?.toString() || "",
      net_cost:
        (
          pricingData.calculatedSupplierPrice || pricingData.supplierPrice
        )?.toString() || "",
      net_price: pricingData.calculatedNetPrice?.toString() || "",
      margin_rate: pricingData.marginRate?.toString() || "",
      selling_price: pricingData.calculatedSellingPrice?.toString() || "",

      // Selling currency fields
      selling_currency: pricingData.sellingCurrency || prev.selling_currency,
      selling_price_selling_currency:
        pricingData.calculatedSellingPriceSellingCurrency?.toString() || "",
      exchange_rate: pricingData.exchangeRate?.toString() || prev.exchange_rate,
      exchange_rate_date: pricingData.exchangeRateDate
        ? pricingData.exchangeRateDate.toISOString().split("T")[0]
        : prev.exchange_rate_date,

      // Store addon line items in add_ons field
      add_ons: pricingData.addonLineItems || [],
    }));
  };

  const handleCustomFieldChange = (fieldKey: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      custom_fields: {
        ...prev.custom_fields,
        [fieldKey]: value,
      },
    }));
  };

  // Handle addon selection changes from custom fields
  const handleAddonSelectionChange = async (
    fieldKey: string,
    selectedAddonIds: string[]
  ) => {
    try {
      // Update custom fields with selected addon IDs
      setFormData((prev) => ({
        ...prev,
        custom_fields: {
          ...prev.custom_fields,
          [fieldKey]: selectedAddonIds,
        },
      }));

      // Create line items from selection with pricing lookup
      const lineItems = await createLineItemsFromSelection(
        selectedAddonIds,
        {
          supplier_id: formData.supplier_id,
          activity_start_date: formData.active_from,
          activity_end_date: formData.active_to,
        },
        productsServices
      );

      // Ensure supplier name is populated from the currently selected supplier
      const enhancedLineItems = lineItems.map(item => ({
        ...item,
        supplier_name: item.supplier_name || selectedSupplier?.name || 'Unknown Supplier'
      }));

      console.log("🔍 Setting addon line items with supplier names:", enhancedLineItems);
      setAddonLineItems(enhancedLineItems);
    } catch (error) {
      console.error("Error handling addon selection change:", error);
      toast.error("Failed to load addon pricing");
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.supplier_id) {
      newErrors.supplier_id = "Supplier is required";
    }

    if (!formData.product_service_id) {
      newErrors.product_service_id = "Product/Service is required";
    }

    // REQUIRED PRICING FIELDS VALIDATION
    // Gross Price (required)
    if (!formData.gross_price || formData.gross_price.trim() === "") {
      newErrors.gross_price = "Gross price is required";
    } else {
      const grossPrice = parseFloat(formData.gross_price);
      if (isNaN(grossPrice) || grossPrice <= 0) {
        newErrors.gross_price = "Gross price must be a positive number";
      }
    }

    // Commission (optional)
    if (formData.commission && formData.commission.trim() !== "") {
      const commission = parseFloat(formData.commission);
      if (isNaN(commission) || commission < 0 || commission > 100) {
        newErrors.commission = "Commission must be between 0% and 100%";
      }
    }

    // Net Cost (required)
    if (!formData.net_cost || formData.net_cost.trim() === "") {
      newErrors.net_cost = "Net cost is required";
    } else {
      const netCost = parseFloat(formData.net_cost);
      if (isNaN(netCost) || netCost <= 0) {
        newErrors.net_cost = "Net cost must be a positive number";
      }
    }

    // Margin Rate (required)
    if (!formData.margin_rate || formData.margin_rate.trim() === "") {
      newErrors.margin_rate = "Margin rate is required";
    } else {
      const marginRate = parseFloat(formData.margin_rate);
      if (isNaN(marginRate) || marginRate < 0 || marginRate > 99.99) {
        newErrors.margin_rate = "Margin rate must be between 0% and 99.99%";
      }
    }

    // Selling Price (required)
    if (!formData.selling_price || formData.selling_price.trim() === "") {
      newErrors.selling_price = "Selling price is required";
    } else {
      const sellingPrice = parseFloat(formData.selling_price);
      if (isNaN(sellingPrice) || sellingPrice <= 0) {
        newErrors.selling_price = "Selling price must be a positive number";
      }
    }

    // Selling Currency (required)
    if (!formData.selling_currency || formData.selling_currency.trim() === "") {
      newErrors.selling_currency = "Selling currency is required";
    } else if (!/^[A-Z]{3}$/.test(formData.selling_currency)) {
      newErrors.selling_currency =
        "Selling currency must be a valid 3-letter code";
    }

    // Exchange Rate (required)
    if (!formData.exchange_rate || formData.exchange_rate.trim() === "") {
      newErrors.exchange_rate = "Exchange rate is required";
    } else {
      const exchangeRate = parseFloat(formData.exchange_rate);
      if (isNaN(exchangeRate) || exchangeRate <= 0) {
        newErrors.exchange_rate = "Exchange rate must be a positive number";
      }
    }

    // Selling Price (Selling Currency) (required)
    if (
      !formData.selling_price_selling_currency ||
      formData.selling_price_selling_currency.trim() === ""
    ) {
      newErrors.selling_price_selling_currency =
        "Selling price (selling currency) is required";
    } else {
      const sellingPriceSellingCurrency = parseFloat(
        formData.selling_price_selling_currency
      );
      if (
        isNaN(sellingPriceSellingCurrency) ||
        sellingPriceSellingCurrency <= 0
      ) {
        newErrors.selling_price_selling_currency =
          "Selling price (selling currency) must be a positive number";
      }
    }

    if (!formData.currency) {
      newErrors.currency = "Currency is required";
    }

    // Validate date range (only if both dates are provided)
    if (formData.active_from && formData.active_to) {
      const fromDate = new Date(formData.active_from);
      const toDate = new Date(formData.active_to);

      if (fromDate >= toDate) {
        newErrors.active_to = "Active To date must be after Active From date";
      }
    }

    // Note: Open-ended dates (empty active_to) are allowed and will be handled by server validation

    // Validate required custom fields (skip locked fields as they're inherited)
    categorySchema.forEach((field) => {
      if (
        field.required &&
        !field.locked_in_offerings &&
        !formData.custom_fields[field.key]
      ) {
        newErrors[`custom_field_${field.key}`] = `${field.label} is required`;
      }

      // For locked fields, ensure they have the correct expected values
      if (field.locked_in_offerings && offering) {
        const expectedValue =
          offering.product_service?.custom_fields?.[field.key];
        const currentValue = formData.custom_fields[field.key];

        // If locked field has been modified, this is not allowed
        if (
          expectedValue !== undefined &&
          JSON.stringify(expectedValue) !== JSON.stringify(currentValue)
        ) {
          // Allow locked fields to submit - they auto-update when Product/Service changes
          // newErrors[`custom_field_${field.key}`] = `${field.label} is locked and cannot be modified`;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("Please fill the required fields before submitting");
      return;
    }

    try {
      // Prepare custom fields: use expected values for locked fields, form values for others
      const filteredCustomFields: Record<string, any> = {};

      // Add all fields from form data
      Object.keys(formData.custom_fields || {}).forEach((fieldKey) => {
        const fieldSchema = categorySchema.find(
          (field: any) => field.key === fieldKey
        );

        if (fieldSchema?.locked_in_offerings && selectedProductService) {
          // For locked fields, use the current value from the currently selected Product/Service
          const currentValue = selectedProductService.custom_fields?.[fieldKey];
          filteredCustomFields[fieldKey] = currentValue;
        } else {
          // For non-locked fields, use the form value
          filteredCustomFields[fieldKey] = formData.custom_fields[fieldKey];
          console.log(`� Using form value for field ${fieldKey}:`, formData.custom_fields[fieldKey]);
        }
      });

      // Debug logs removed

      const updateData = {
        // Include product_service_id if it has changed
        product_service_id: formData.product_service_id !== offering?.product_service_id
          ? formData.product_service_id
          : undefined,
        custom_fields: filteredCustomFields,
        active_from: formData.active_from || undefined,
        active_to: formData.active_to || undefined,
        availability_notes: formData.availability_notes || undefined,

        // Legacy cost field
        cost: formData.cost ? parseFloat(formData.cost) : undefined,

        // Enhanced pricing fields
        commission: formData.commission
          ? parseFloat(formData.commission)
          : undefined,
        gross_price: formData.gross_price
          ? parseFloat(formData.gross_price)
          : undefined,
        net_cost: formData.net_cost ? parseFloat(formData.net_cost) : undefined,
        margin_rate: formData.margin_rate
          ? parseFloat(formData.margin_rate)
          : undefined,
        selling_price: formData.selling_price
          ? parseFloat(formData.selling_price)
          : undefined,

        // Currency fields
        currency: formData.currency,
        currency_override: formData.currency_override,

        // Selling currency fields
        selling_currency: formData.selling_currency,
        selling_price_selling_currency: formData.selling_price_selling_currency
          ? parseFloat(formData.selling_price_selling_currency)
          : undefined,
        exchange_rate: formData.exchange_rate
          ? parseFloat(formData.exchange_rate)
          : undefined,
        exchange_rate_date: formData.exchange_rate_date
          ? new Date(formData.exchange_rate_date)
          : undefined,

        status: formData.status,

        // Add-ons data with mandatory flags (use formData.add_ons as single source of truth)
        add_ons: formData.add_ons || [],
      };

      await updateSupplierOffering.mutateAsync({ id: id!, data: updateData });

      // Show success message
      toast.success("Supplier offering updated successfully");

      // Small delay to ensure cache invalidation completes before navigation
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Navigate back to detail view
      navigate(`/supplier-management/supplier-offerings/${id}`);
    } catch (error: any) {
      console.error("Error updating supplier offering:", error);

      // Handle specific error types
      if (error?.response?.status === 409) {
        // Handle validation errors from the server
        let errorMessage =
          "This supplier offering configuration already exists.";

        if (error?.response?.data?.message) {
          errorMessage = error.response.data.message;
        } else if (error?.message) {
          errorMessage = error.message;
        }

        // Provide specific guidance for different types of conflicts
        if (errorMessage.includes("Date range conflict")) {
          // Already has specific date information
        } else if (errorMessage.includes("open-ended validity")) {
          // Already has specific open-ended conflict information
        } else if (errorMessage.includes("Cannot create offering")) {
          // Already has specific creation conflict information
        }

        toast.error(errorMessage);
      } else if (error?.response?.status === 400) {
        // Handle validation errors
        let errorMessage = "Invalid data provided.";

        if (error?.response?.data?.message) {
          errorMessage = error.response.data.message;
        } else if (error?.message) {
          errorMessage = error.message;
        }

        toast.error(errorMessage);
      } else {
        toast.error(error?.message || "Failed to update supplier offering");
      }
    }
  };

  if (offeringLoading) {
    return (
      <Container className="p-6">
        <div className="text-center">
          <Text>Loading supplier offering...</Text>
        </div>
      </Container>
    );
  }

  if (error || !offering) {
    return (
      <Container className="p-6">
        <div className="text-center">
          <Text className="text-ui-fg-error">
            {error?.message || "Supplier offering not found"}
          </Text>
          <Button
            variant="secondary"
            onClick={() => navigate("/supplier-management/supplier-offerings")}
            className="mt-4"
          >
            Back to Supplier Offerings
          </Button>
        </div>
      </Container>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-x-4">
            <Button
              variant="transparent"
              onClick={() =>
                navigate(`/supplier-management/supplier-offerings/`)
              }
              className="p-1"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <Heading level="h2">Edit Supplier Offering</Heading>
              <Text className="text-ui-fg-subtle">
                {offering.product_service?.name} by {offering.supplier?.name}
              </Text>
            </div>
          </div>
          <div className="flex items-center gap-x-2">
            <Button
              variant="secondary"
              onClick={() =>
                navigate(`/supplier-management/supplier-offerings/${id}`)
              }
            >
              <X className="h-4 w-4" />
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              isLoading={updateSupplierOffering.isPending}
            >
              <Save className="h-4 w-4" />
              Update Offering
            </Button>
          </div>
        </div>

        {/* Form */}
        <div className="px-6 py-6">
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Basic Information */}
            <div className="space-y-6">
              <div>
                <Heading level="h3">Basic Information</Heading>
                <Text className="text-ui-fg-subtle">
                  Select the supplier and product/service for this offering. The
                  category will be auto-filled and custom fields will load based
                  on your selection.
                </Text>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="supplier_id">
                    Supplier <span className="text-ui-fg-error">*</span>
                  </Label>
                  <Select
                    key={`supplier-${formData.supplier_id}`}
                    value={formData.supplier_id || ""}
                    onValueChange={(value) =>
                      handleInputChange("supplier_id", value)
                    }
                    disabled={suppliersLoading}
                  >
                    <Select.Trigger>
                      <Select.Value placeholder="Select a supplier" />
                    </Select.Trigger>
                    <Select.Content>
                      {suppliers.map((supplier: any) => (
                        <Select.Item key={supplier.id} value={supplier.id}>
                          {supplier.name}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                  {errors.supplier_id && (
                    <Text size="small" className="text-ui-fg-error">
                      {errors.supplier_id}
                    </Text>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="product_service_id">
                    Product/Service <span className="text-ui-fg-error">*</span>
                  </Label>
                  <Select
                    key={`product-service-${formData.product_service_id}`}
                    value={formData.product_service_id || ""}
                    onValueChange={(value) =>
                      handleInputChange("product_service_id", value)
                    }
                    disabled={productsServicesLoading}
                  >
                    <Select.Trigger>
                      <Select.Value placeholder="Select a product/service" />
                    </Select.Trigger>
                    <Select.Content>
                      {productsServices.map((ps: any) => (
                        <Select.Item key={ps.id} value={ps.id}>
                          <TruncatedSelectItem
                            text={`${ps.name} (${
                              ps.category?.name || ps.type
                            })`}
                            maxLength={100}
                          />
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                  {errors.product_service_id && (
                    <Text size="small" className="text-ui-fg-error">
                      {errors.product_service_id}
                    </Text>
                  )}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Status</Label>
              <div className="flex gap-6">
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="radio"
                    name="status"
                    value="active"
                    checked={formData.status === "active"}
                    onChange={(e) =>
                      handleInputChange("status", e.target.value)
                    }
                    className="w-4 h-4 text-ui-fg-interactive border-ui-border-base focus:ring-ui-border-interactive"
                  />
                  <Text size="small">Active</Text>
                </label>
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="radio"
                    name="status"
                    value="inactive"
                    checked={formData.status === "inactive"}
                    onChange={(e) =>
                      handleInputChange("status", e.target.value)
                    }
                    className="w-4 h-4 text-ui-fg-interactive border-ui-border-base focus:ring-ui-border-interactive"
                  />
                  <Text size="small">Inactive</Text>
                </label>
              </div>
            </div>

            {/* Currency Selection */}
            <div className="">
              <div className="space-y-2">
                <Label htmlFor="currency">
                  Currency <span className="text-ui-fg-error">*</span>
                </Label>
                <div className="space-y-3">
                  {selectedSupplier?.default_currency &&
                  !formData.currency_override ? (
                    <div className="space-y-3">
                      <div className="relative">
                        <Input
                          id="currency"
                          value={selectedSupplier.default_currency}
                          readOnly
                          className="bg-ui-bg-subtle cursor-not-allowed"
                          placeholder="Currency will be set from supplier"
                        />
                        <Text size="small" className="text-ui-fg-subtle mt-1">
                          Using supplier's default currency
                        </Text>
                      </div>
                      {/* <Button
                                    type="button"
                                    variant="secondary"
                                    size="small"
                                    onClick={() =>
                                      handleInputChange("currency_override", true)
                                    }
                                    className="w-full"
                                  >
                                    Change Currency
                                  </Button> */}
                    </div>
                  ) : !selectedSupplier ? (
                    <div className="relative">
                      <Input
                        id="currency"
                        value=""
                        readOnly
                        className="bg-ui-bg-subtle cursor-not-allowed"
                        placeholder="Select a supplier first"
                      />
                      <Text size="small" className="text-ui-fg-subtle mt-1">
                        Currency will be set from the supplier's default
                        currency
                      </Text>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Select
                        value={formData.currency}
                        onValueChange={(value) =>
                          handleInputChange("currency", value)
                        }
                      >
                        <Select.Trigger>
                          <Select.Value placeholder="Select currency" />
                        </Select.Trigger>
                        <Select.Content>
                          <Select.Item value="CHF">
                            CHF - Swiss Franc
                          </Select.Item>
                          <Select.Item value="EUR">EUR - Euro</Select.Item>
                          <Select.Item value="USD">USD - US Dollar</Select.Item>
                          <Select.Item value="GBP">
                            GBP - British Pound
                          </Select.Item>
                          <Select.Item value="CAD">
                            CAD - Canadian Dollar
                          </Select.Item>
                          <Select.Item value="AUD">
                            AUD - Australian Dollar
                          </Select.Item>
                          <Select.Item value="JPY">
                            JPY - Japanese Yen
                          </Select.Item>
                        </Select.Content>
                      </Select>

                      {selectedSupplier?.default_currency && (
                        <div className="flex items-center justify-between p-2 bg-blue-50 border border-blue-200 rounded">
                          <Text size="small" className="text-blue-700">
                            🔄 Currency override is active
                          </Text>
                          <button
                            type="button"
                            onClick={() => {
                              handleInputChange("currency_override", false);
                              handleInputChange(
                                "currency",
                                selectedSupplier.default_currency
                              );
                            }}
                            className="text-xs text-blue-600 hover:text-blue-800 font-medium underline"
                          >
                            Use Default ({selectedSupplier.default_currency})
                          </button>
                        </div>
                      )}
                    </div>
                  )}

                  {selectedSupplier?.default_currency &&
                    formData.currency_override &&
                    formData.currency !== selectedSupplier.default_currency && (
                      <div className="flex items-start gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded">
                        <Text size="small" className="text-yellow-700">
                          ⚠️ <strong>Currency Mismatch:</strong> You selected{" "}
                          {formData.currency} but the supplier's default is{" "}
                          {selectedSupplier.default_currency}. This may affect
                          pricing calculations and financial reporting.
                        </Text>
                      </div>
                    )}

                  {errors.currency && (
                    <Text size="small" className="text-ui-fg-error">
                      {errors.currency}
                    </Text>
                  )}
                </div>
              </div>
            </div>

            {/* Enhanced Pricing */}
            <div className="space-y-6">
              <PricingCalculator
                initialData={{
                  commission: formData.commission
                    ? parseFloat(formData.commission)
                    : undefined,
                  grossPrice: formData.gross_price
                    ? parseFloat(formData.gross_price)
                    : undefined,
                  supplierPrice: formData.net_cost
                    ? parseFloat(formData.net_cost)
                    : undefined,
                  marginRate: formData.margin_rate
                    ? parseFloat(formData.margin_rate)
                    : undefined,
                  sellingPrice: formData.selling_price
                    ? parseFloat(formData.selling_price)
                    : undefined,

                  // Currency fields
                  currency: formData.currency,

                  // Selling currency fields
                  sellingCurrency: formData.selling_currency,
                  sellingPriceSellingCurrency:
                    formData.selling_price_selling_currency
                      ? parseFloat(formData.selling_price_selling_currency)
                      : undefined,
                  exchangeRate: formData.exchange_rate
                    ? parseFloat(formData.exchange_rate)
                    : undefined,
                  exchangeRateDate: formData.exchange_rate_date
                    ? new Date(formData.exchange_rate_date)
                    : undefined,

                  // Add-ons data
                  addonLineItems: addonLineItems,
                }}
                onChange={handlePricingChange}
                costCurrency={formData.currency || "CHF"}
                errors={errors}
                showValidation={true}
                defaultSupplierId={formData.supplier_id}
                availableAddons={getAvailableAddons()}
                currentlySelectedAddons={getCurrentlySelectedAddons()}
                onAddonSelectionChange={(addonIds) => {
                  // Handle addon selection from the "Add Rows" button
                  if (addonIds.length > 0) {
                    // Find the first addon field to trigger the selection change
                    const addonField = categorySchema.find(field => field.type === "addons");
                    if (addonField) {
                      handleAddonSelectionChange(addonField.key, addonIds);
                    }
                  }
                }}
              />
            </div>

            {/* Validity Period */}
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="active_from">Active From</Label>
                  <Input
                    id="active_from"
                    type="date"
                    value={formData.active_from}
                    onChange={(e) =>
                      handleInputChange("active_from", e.target.value)
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="active_to">
                    Active To
                    <span className="text-xs text-ui-fg-subtle ml-1">
                      (Leave empty for open-ended)
                    </span>
                  </Label>
                  <Input
                    id="active_to"
                    type="date"
                    value={formData.active_to}
                    onChange={(e) =>
                      handleInputChange("active_to", e.target.value)
                    }
                  />
                  <div className="flex items-center gap-2">
                    <button
                      type="button"
                      onClick={() => handleInputChange("active_to", "")}
                      className="text-xs text-ui-fg-subtle hover:text-ui-fg-base underline"
                    >
                      Clear (make open-ended)
                    </button>
                    {!formData.active_to && (
                      <Text size="small" className="text-ui-fg-subtle">
                        This offering will have no end date
                      </Text>
                    )}
                  </div>
                  {errors.active_to && (
                    <Text size="small" className="text-ui-fg-error">
                      {errors.active_to}
                    </Text>
                  )}
                </div>
              </div>
            </div>

            {/* Availability */}
            <div className="space-y-6">
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="availability_notes">Availability Notes</Label>
                  <Textarea
                    id="availability_notes"
                    placeholder="Optional notes about availability, restrictions, or special conditions..."
                    value={formData.availability_notes}
                    onChange={(e) =>
                      handleInputChange("availability_notes", e.target.value)
                    }
                    rows={3}
                  />
                </div>
              </div>
            </div>

            {/* Dynamic Fields */}
            {categorySchema.length > 0 && (
              <div className="space-y-6">
                <DynamicFieldRenderer
                  schema={categorySchema}
                  values={formData.custom_fields}
                  onChange={handleCustomFieldChange}
                  errors={Object.fromEntries(
                    Object.entries(errors)
                      .filter(([key]) => key.startsWith("custom_field_"))
                      .map(([key, value]) => [
                        key.replace("custom_field_", ""),
                        value,
                      ])
                  )}
                  inheritedValues={selectedProductService?.custom_fields || {}}
                  showInheritanceIndicators={true}
                  fieldContext="supplier"
                  onAddonSelectionChange={handleAddonSelectionChange}
                  excludeProductServiceId={formData.product_service_id}
                />
              </div>
            )}
          </form>
        </div>
      </Container>

      <Toaster />
    </>
  );
};

export const config = defineRouteConfig({
  label: "Edit Supplier Offering",
});

export default EditSupplierOfferingPage;

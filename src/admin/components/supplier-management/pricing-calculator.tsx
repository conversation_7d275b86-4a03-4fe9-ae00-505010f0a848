import React, { useState, useEffect, useRef } from "react";
import { Input, Text, Select, Button } from "@camped-ai/ui";
import { Calculator, Plus } from "lucide-react";
import { AddonLineItem } from "./addon-line-item";
import { useSuppliers } from "../../hooks/vendor-management/use-suppliers";
import { MultiSelect } from "../common/MultiSelect";
import TruncatedSelectItem from "../common/TruncatedSelectItem";

interface PricingData {
  commission?: number;
  grossPrice?: number; // Gross price
  supplierPrice?: number; // Net cost to supplier
  marginRate?: number;
  sellingPrice?: number;

  // Currency fields
  currency?: string; // Cost currency

  // Selling currency fields
  sellingCurrency?: string;
  sellingPriceSellingCurrency?: number;
  exchangeRate?: number;
  exchangeRateDate?: Date;

  // Addon line items
  addonLineItems?: AddonLineItem[];
}

interface PricingCalculatorProps {
  initialData?: PricingData;
  onChange?: (
    data: PricingData & {
      calculatedSupplierPrice?: number;
      calculatedNetPrice?: number;
      calculatedSellingPrice?: number;
      calculatedSellingPriceSellingCurrency?: number;
      calculatedTotalWithAddons?: number;
      calculatedTotalWithAddonsSellingCurrency?: number;
      addonsTotalPrice?: number;
      isPricingComplete?: boolean;
      pricingErrors?: string[];
    }
  ) => void;
  disabled?: boolean;
  costCurrency?: string; // The cost currency (e.g., CHF)
  onAddonSelectionChange?: (addonIds: string[]) => void; // Callback for when addons are selected from custom fields
  errors?: Record<string, string>; // Validation errors from parent form
  showValidation?: boolean; // Whether to show validation errors
  defaultSupplierId?: string; // Default supplier ID to pre-select for addons
  availableAddons?: { value: string; label: string }[]; // Available addons from custom fields
  currentlySelectedAddons?: string[]; // Currently selected addon IDs from custom fields
}

const PricingCalculator: React.FC<PricingCalculatorProps> = ({
  initialData = {},
  onChange,
  disabled = false,
  costCurrency = "CHF",
  onAddonSelectionChange,
  errors: validationErrors = {},
  showValidation = true,
  defaultSupplierId,
  availableAddons = [],
  currentlySelectedAddons = [],
}) => {
  const [pricingData, setPricingData] = useState<PricingData>({});
  const [errors, setErrors] = useState<string[]>([]);
  const [addonLineItems, setAddonLineItems] = useState<AddonLineItem[]>([]);
  const initializedRef = useRef(false);

  // Modal state for addon selection
  const [showAddonModal, setShowAddonModal] = useState(false);
  const [selectedAddonIds, setSelectedAddonIds] = useState<string[]>([]);

  // Fetch suppliers for addon supplier selection
  const { data: suppliersData } = useSuppliers({
    status: "Active",
    limit: 100,
  });
  const suppliers = suppliersData?.suppliers || [];

  // State to track supplier offerings for each addon
  const [addonSupplierOfferings, setAddonSupplierOfferings] = useState<Record<string, any[]>>(
    {}
  );

  // Function to get supplier offerings for a specific addon
  const getSupplierOfferingsForAddon = (addonId: string) => {
    return addonSupplierOfferings[addonId] || [];
  };

  // Function to fetch supplier offerings for a specific addon
  const fetchSupplierOfferingsForAddon = async (addonId: string) => {
    try {
      const response = await fetch(
        `/admin/supplier-management/supplier-offerings?product_service_id=${addonId}&status=active&limit=100`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch supplier offerings");
      }

      const data = await response.json();
      const offerings = data.supplier_offerings || [];

      // Enrich offerings with supplier information
      const enrichedOfferings = offerings.map((offering: any) => {
        const supplier = suppliers.find(s => s.id === offering.supplier_id);
        return {
          ...offering,
          supplier_name: supplier?.name || 'Unknown Supplier',
          display_name: `${supplier?.name || 'Unknown Supplier'} - ${offering.product_service?.name || 'Unknown Product'}`
        };
      });

      setAddonSupplierOfferings((prev) => ({
        ...prev,
        [addonId]: enrichedOfferings,
      }));

      return enrichedOfferings;
    } catch (error) {
      console.error("Error fetching supplier offerings for addon:", error);
      return [];
    }
  };

  // Fetch supplier offerings for addons when addon line items change
  useEffect(() => {
    addonLineItems.forEach((addon) => {
      if (addon.addon_id && !addonSupplierOfferings[addon.addon_id]) {
        fetchSupplierOfferingsForAddon(addon.addon_id);
      }
    });
  }, [addonLineItems, suppliers]);

  // Auto-populate pricing for addons with default supplier when suppliers are loaded
  useEffect(() => {
    if (defaultSupplierId && suppliers.length > 0) {
      addonLineItems.forEach(async (addon) => {
        // Only auto-populate if addon has default supplier but no pricing data yet
        if (
          addon.supplier_id === defaultSupplierId &&
          addon.addon_id &&
          !addon.gross_price &&
          !addon.commission &&
          !addon.net_cost
        ) {
          const pricing = await getSupplierOfferingPricing(
            addon.addon_id,
            defaultSupplierId
          );
          if (pricing) {
            setAddonLineItems((prev) =>
              prev.map((item) =>
                item.id === addon.id
                  ? {
                      ...item,
                      // Auto-populate pricing from supplier offering
                      gross_price: pricing.gross_price,
                      commission: pricing.commission,
                      net_cost: pricing.net_cost,
                      margin_rate: pricing.margin_rate,
                      selling_price: pricing.selling_price,
                      currency: pricing.currency,
                      selling_currency: pricing.selling_currency,
                      exchange_rate: pricing.exchange_rate,
                      selling_price_selling_currency:
                        pricing.selling_price_selling_currency,
                    }
                  : item
              )
            );
          }
        }
      });
    }
  }, [addonLineItems, defaultSupplierId, suppliers]);

  // Function to get supplier offering pricing for an addon
  const getSupplierOfferingPricing = async (
    addonId: string,
    supplierId: string
  ) => {
    try {
      // Fetch supplier offerings for this addon and supplier
      const response = await fetch(
        `/admin/supplier-management/supplier-offerings?product_service_id=${addonId}&supplier_id=${supplierId}&status=active&limit=1`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch supplier offering");
      }

      const data = await response.json();
      const offerings = data.supplier_offerings || [];

      if (offerings.length > 0) {
        const offering = offerings[0];
        return {
          gross_price: offering.gross_price
            ? parseFloat(offering.gross_price)
            : undefined,
          commission: offering.commission
            ? parseFloat(offering.commission) * 100
            : undefined, // Convert to percentage
          net_cost: offering.net_cost
            ? parseFloat(offering.net_cost)
            : undefined,
          margin_rate: offering.margin_rate
            ? parseFloat(offering.margin_rate) * 100
            : undefined, // Convert to percentage
          selling_price: offering.selling_price
            ? parseFloat(offering.selling_price)
            : undefined,
          currency: offering.currency,
          selling_currency: offering.selling_currency,
          exchange_rate: offering.exchange_rate
            ? parseFloat(offering.exchange_rate)
            : undefined,
          selling_price_selling_currency:
            offering.selling_price_selling_currency
              ? parseFloat(offering.selling_price_selling_currency)
              : undefined,
        };
      }

      return null;
    } catch (error) {
      console.error("Error fetching supplier offering pricing:", error);
      return null;
    }
  };

  // Initialize data only once when initialData has meaningful values
  useEffect(() => {
    // Check if initialData has any meaningful values and we haven't initialized yet
    const hasData =
      initialData.commission !== undefined ||
      initialData.grossPrice !== undefined ||
      initialData.supplierPrice !== undefined ||
      initialData.marginRate !== undefined ||
      initialData.sellingPrice !== undefined ||
      (initialData.addonLineItems && initialData.addonLineItems.length > 0);

    if (hasData && !initializedRef.current) {
      setPricingData({
        commission: initialData.commission,
        grossPrice: initialData.grossPrice,
        supplierPrice: initialData.supplierPrice,
        marginRate: initialData.marginRate,
        sellingPrice: initialData.sellingPrice,
        currency: initialData.currency,
        sellingCurrency: initialData.sellingCurrency,
        sellingPriceSellingCurrency: initialData.sellingPriceSellingCurrency,
        exchangeRate: initialData.exchangeRate,
        exchangeRateDate: initialData.exchangeRateDate,
      });

      if (initialData.addonLineItems) {
        // Set default supplier for addons that don't have a supplier selected
        const addonsWithDefaultSupplier = initialData.addonLineItems.map(
          (addon) => ({
            ...addon,
            supplier_id: addon.supplier_id || defaultSupplierId || undefined,
          })
        );
        setAddonLineItems(addonsWithDefaultSupplier);
      }

      initializedRef.current = true;
    }
  }, [initialData]);

  // Separate effect to handle addon line items updates after initialization
  useEffect(() => {
    if (initializedRef.current && initialData.addonLineItems) {
      // Set default supplier for addons that don't have a supplier selected
      const addonsWithDefaultSupplier = initialData.addonLineItems.map(
        (addon) => ({
          ...addon,
          supplier_id: addon.supplier_id || defaultSupplierId || undefined,
        })
      );
      setAddonLineItems(addonsWithDefaultSupplier);
    }
  }, [initialData.addonLineItems, defaultSupplierId]);

  // Calculate derived values
  const calculatePricing = (data: PricingData) => {
    const calculationErrors: string[] = [];
    let calculatedSupplierPrice: number | undefined;
    let calculatedSellingPrice: number | undefined;

    try {
      // Ensure numeric values
      const commission =
        typeof data.commission === "string"
          ? parseFloat(data.commission)
          : data.commission;
      const grossPrice =
        typeof data.grossPrice === "string"
          ? parseFloat(data.grossPrice)
          : data.grossPrice;
      const supplierPrice =
        typeof data.supplierPrice === "string"
          ? parseFloat(data.supplierPrice)
          : data.supplierPrice;
      const marginRate =
        typeof data.marginRate === "string"
          ? parseFloat(data.marginRate)
          : data.marginRate;

      // Calculate difference value: Gross Price × Commission (if both provided)
      let differenceValue: number | undefined;
      if (
        commission !== undefined &&
        grossPrice !== undefined &&
        !isNaN(commission) &&
        !isNaN(grossPrice)
      ) {
        if (commission < 0 || commission > 1) {
          calculationErrors.push(
            "Commission must be between 0 and 1 (0% to 100%)"
          );
        } else if (grossPrice < 0) {
          calculationErrors.push("Gross price must be non-negative");
        } else {
          differenceValue = grossPrice * commission;
          // Calculate Net Cost: Gross Price - Difference Value
          calculatedSupplierPrice = grossPrice - differenceValue;
        }
      }

      // Use calculated or manually entered net cost
      const finalSupplierPrice =
        calculatedSupplierPrice ||
        (supplierPrice && !isNaN(supplierPrice) ? supplierPrice : undefined);

      // Calculate selling price: Net Cost ÷ (1 - Margin Rate)
      if (
        finalSupplierPrice &&
        marginRate !== undefined &&
        !isNaN(marginRate)
      ) {
        if (marginRate < 0 || marginRate >= 1) {
          calculationErrors.push(
            "Margin rate must be between 0 and 1 (0% to 99.99%)"
          );
        } else {
          calculatedSellingPrice = finalSupplierPrice / (1 - marginRate);
        }
      }
    } catch (error) {
      calculationErrors.push("Error in pricing calculations");
    }

    // Calculate selling price in selling currency
    let calculatedSellingPriceSellingCurrency: number | undefined;
    if (calculatedSellingPrice && data.sellingCurrency && data.exchangeRate) {
      const exchangeRate =
        typeof data.exchangeRate === "string"
          ? parseFloat(data.exchangeRate)
          : data.exchangeRate;
      if (data.sellingCurrency === costCurrency) {
        // Same currency, no conversion needed
        calculatedSellingPriceSellingCurrency = calculatedSellingPrice;
      } else if (!isNaN(exchangeRate)) {
        // Convert using exchange rate
        calculatedSellingPriceSellingCurrency =
          calculatedSellingPrice * exchangeRate;
      }
    }

    // Pricing is complete if we have either:
    // 1. Commission + Gross Price (calculated net cost), OR
    // 2. Direct net cost entry
    // Plus margin rate and selling price
    // Calculate addon totals
    const addonsTotalPrice = addonLineItems.reduce((sum, item) => {
      return sum + (item.selling_price || 0);
    }, 0);

    // Calculate total with addons
    let calculatedTotalWithAddons: number | undefined;
    let calculatedTotalWithAddonsSellingCurrency: number | undefined;

    if (calculatedSellingPrice !== undefined) {
      calculatedTotalWithAddons = calculatedSellingPrice + addonsTotalPrice;

      // Convert total to selling currency if needed
      if (calculatedSellingPriceSellingCurrency !== undefined) {
        const exchangeRate =
          typeof data.exchangeRate === "string"
            ? parseFloat(data.exchangeRate)
            : data.exchangeRate;

        if (data.sellingCurrency === costCurrency) {
          calculatedTotalWithAddonsSellingCurrency = calculatedTotalWithAddons;
        } else if (exchangeRate && !isNaN(exchangeRate)) {
          calculatedTotalWithAddonsSellingCurrency =
            calculatedTotalWithAddons * exchangeRate;
        }
      }
    }

    const hasNetCost = !!(calculatedSupplierPrice || data.supplierPrice);
    const isPricingComplete = !!(
      hasNetCost &&
      data.marginRate !== undefined &&
      calculatedSellingPrice
    );

    return {
      calculatedSupplierPrice,
      calculatedSellingPrice,
      calculatedSellingPriceSellingCurrency,
      calculatedTotalWithAddons,
      calculatedTotalWithAddonsSellingCurrency,
      addonsTotalPrice,
      isPricingComplete,
      pricingErrors: calculationErrors,
    };
  };

  // Update calculations when data changes
  useEffect(() => {
    const calculations = calculatePricing(pricingData);
    setErrors(calculations.pricingErrors);

    if (onChange) {
      onChange({
        ...pricingData,
        addonLineItems,
        ...calculations,
      });
    }
  }, [pricingData, addonLineItems, onChange]);

  const handleInputChange = (
    field: keyof PricingData,
    value: number | undefined
  ) => {
    setPricingData((prev) => {
      const newData = {
        ...prev,
        [field]: value,
      };

      // If commission is being set and we have gross price, clear manual supplier price
      if (
        field === "commission" &&
        value !== undefined &&
        prev.grossPrice !== undefined
      ) {
        newData.supplierPrice = undefined;
      }

      // If gross price is being set and we have commission, clear manual supplier price
      if (
        field === "grossPrice" &&
        value !== undefined &&
        prev.commission !== undefined
      ) {
        newData.supplierPrice = undefined;
      }

      return newData;
    });
  };

  // Handle addon input changes
  const handleAddonInputChange = async (
    addonId: string,
    field: keyof AddonLineItem,
    value: any
  ) => {
    // If supplier offering is being changed, auto-populate pricing from the selected offering
    if (field === "supplier_offering_id" && value) {
      const addon = addonLineItems.find((item) => item.id === addonId);
      if (addon?.addon_id) {
        // Find the selected offering from our cached data
        const availableOfferings = getSupplierOfferingsForAddon(addon.addon_id);
        const selectedOffering = availableOfferings.find(offering => offering.id === value);

        if (selectedOffering) {
          // Extract pricing data from the supplier offering
          const pricing = {
            gross_price: selectedOffering.gross_price ? parseFloat(selectedOffering.gross_price.toString()) : undefined,
            commission: selectedOffering.commission ? parseFloat(selectedOffering.commission.toString()) * 100 : undefined, // Convert to percentage
            net_cost: selectedOffering.net_cost ? parseFloat(selectedOffering.net_cost.toString()) : undefined,
            margin_rate: selectedOffering.margin_rate ? parseFloat(selectedOffering.margin_rate.toString()) * 100 : undefined, // Convert to percentage
            selling_price: selectedOffering.selling_price ? parseFloat(selectedOffering.selling_price.toString()) : undefined,
            currency: selectedOffering.currency,
            selling_currency: selectedOffering.selling_currency,
            exchange_rate: selectedOffering.exchange_rate ? parseFloat(selectedOffering.exchange_rate.toString()) : undefined,
            selling_price_selling_currency: selectedOffering.selling_price_selling_currency ? parseFloat(selectedOffering.selling_price_selling_currency.toString()) : undefined,
          };

          setAddonLineItems((prev) =>
            prev.map((item) =>
              item.id === addonId
                ? {
                    ...item,
                    [field]: value,
                    // Auto-populate pricing from supplier offering
                    gross_price: pricing.gross_price,
                    commission: pricing.commission,
                    net_cost: pricing.net_cost,
                    net_cost_manual: undefined, // Clear manual override
                    margin_rate: pricing.margin_rate,
                    selling_price: pricing.selling_price,
                    currency: pricing.currency,
                    selling_currency: pricing.selling_currency,
                    exchange_rate: pricing.exchange_rate,
                    selling_price_selling_currency: pricing.selling_price_selling_currency,
                  }
                : item
            )
          );
          return;
        }
      }
    }

    // Legacy support: If supplier is being changed, auto-populate pricing from supplier offering
    if (field === "supplier_id" && value) {
      const addon = addonLineItems.find((item) => item.id === addonId);
      if (addon?.addon_id) {
        const pricing = await getSupplierOfferingPricing(addon.addon_id, value);
        if (pricing) {
          setAddonLineItems((prev) =>
            prev.map((item) =>
              item.id === addonId
                ? {
                    ...item,
                    [field]: value,
                    // Auto-populate pricing from supplier offering
                    gross_price: pricing.gross_price,
                    commission: pricing.commission,
                    net_cost: pricing.net_cost,
                    net_cost_manual: undefined, // Clear manual override
                    margin_rate: pricing.margin_rate,
                    selling_price: pricing.selling_price,
                    currency: pricing.currency,
                    selling_currency: pricing.selling_currency,
                    exchange_rate: pricing.exchange_rate,
                    selling_price_selling_currency:
                      pricing.selling_price_selling_currency,
                  }
                : item
            )
          );
          return;
        }
      }
    }

    // Default behavior for other field changes
    setAddonLineItems((prev) =>
      prev.map((addon) =>
        addon.id === addonId ? { ...addon, [field]: value } : addon
      )
    );
  };

  const calculations = calculatePricing(pricingData);

  // Debug logging (removed to prevent infinite calls)

  // Handle addon selection from modal
  const handleAddonModalSave = async () => {
    if (selectedAddonIds.length === 0) {
      setShowAddonModal(false);
      return;
    }

    try {
      // Call the parent's addon selection change handler
      if (onAddonSelectionChange) {
        onAddonSelectionChange(selectedAddonIds);
      }

      // Reset modal state
      setSelectedAddonIds([]);
      setShowAddonModal(false);
    } catch (error) {
      console.error("Error adding addon rows:", error);
    }
  };

  const handleAddonModalCancel = () => {
    setSelectedAddonIds([]);
    setShowAddonModal(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Calculator className="h-5 w-5" />
          <Text weight="plus" size="large">
            Pricing Calculator
          </Text>
        </div>
        {availableAddons.length > 0 && (
          <Button
            type="button"
            variant="secondary"
            size="small"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              // Initialize modal with currently selected addons
              setSelectedAddonIds(currentlySelectedAddons);
              setShowAddonModal(true);
            }}
            disabled={disabled}
          >
            <Plus className="h-4 w-4" />
            Add Rows
          </Button>
        )}
      </div>

      {/* Basic Pricing Fields - Horizontal Table Format */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse border border-ui-border-base">
          <thead>
            <tr className="bg-ui-bg-subtle">
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[250px]">
                Supplier Offering
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[120px]">
                Gross Cost <span className="text-red-500">*</span>
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[120px]">
                Commission (%)
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[120px]">
                Net Cost <span className="text-red-500">*</span>
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[120px]">
                Margin Rate (%) <span className="text-red-500">*</span>
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[120px]">
                Selling Price <span className="text-red-500">*</span>
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[140px]">
                Selling Currency <span className="text-red-500">*</span>
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[120px]">
                Exchange Rate <span className="text-red-500">*</span>
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[160px]">
                Selling Price (Selling Currency){" "}
                <span className="text-red-500">*</span>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              {/* Supplier Offering (Combined Line Item + Supplier) */}
              <td className="border border-ui-border-base px-2 py-2">
                <div className="space-y-1">
                  <Text weight="plus" size="small" className="text-gray-900">
                    Main Product/Service
                  </Text>
                  {defaultSupplierId ? (
                    <Text size="small" className="text-gray-600">
                      {(() => {
                        const supplier = suppliers.find(
                          (s) => s.id === defaultSupplierId
                        );
                        return supplier ? supplier.name : "Unknown Supplier";
                      })()}
                    </Text>
                  ) : (
                    <Text size="small" className="text-gray-600">
                      No supplier selected
                    </Text>
                  )}
                </div>
              </td>
              {/* Gross Price */}
              <td className="border border-ui-border-base px-2 py-2">
                <div className="space-y-1">
                  <Input
                    id="grossPrice"
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="100.00"
                    value={pricingData.grossPrice?.toString() || ""}
                    onChange={(e) => {
                      const value = e.target.value
                        ? parseFloat(e.target.value)
                        : undefined;
                      handleInputChange("grossPrice", value);
                    }}
                    disabled={disabled}
                    className={`w-full text-sm ${
                      showValidation && validationErrors.gross_price
                        ? "border-red-500 focus:border-red-500"
                        : ""
                    }`}
                  />
                  {showValidation && validationErrors.gross_price && (
                    <Text size="small" className="text-red-500">
                      {validationErrors.gross_price}
                    </Text>
                  )}
                </div>
              </td>
              <td className="border border-ui-border-base px-2 py-2">
                <div className="space-y-1">
                  <Input
                    id="commission"
                    type="number"
                    step="0.01"
                    min="0"
                    max="100"
                    placeholder="10"
                    value={
                      pricingData.commission !== undefined
                        ? (pricingData.commission * 100).toString()
                        : ""
                    }
                    onChange={(e) => {
                      const value =
                        e.target.value !== ""
                          ? parseFloat(e.target.value) / 100
                          : undefined;
                      handleInputChange("commission", value);
                    }}
                    disabled={disabled}
                    className={`w-full text-sm ${
                      showValidation && validationErrors.commission
                        ? "border-red-500 focus:border-red-500"
                        : ""
                    }`}
                  />
                  {showValidation && validationErrors.commission && (
                    <Text size="small" className="text-red-500">
                      {validationErrors.commission}
                    </Text>
                  )}
                </div>
              </td>
              <td className="border border-ui-border-base px-2 py-2">
                <div className="space-y-1">
                  <Input
                    id="supplierPrice"
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder={
                      calculations.calculatedSupplierPrice
                        ? calculations.calculatedSupplierPrice.toFixed(2)
                        : "90.00"
                    }
                    value={
                      pricingData.supplierPrice?.toString() ||
                      (calculations.calculatedSupplierPrice
                        ? calculations.calculatedSupplierPrice.toString()
                        : "")
                    }
                    onChange={(e) => {
                      const value = e.target.value
                        ? parseFloat(e.target.value)
                        : undefined;
                      handleInputChange("supplierPrice", value);
                    }}
                    disabled={
                      disabled ||
                      (pricingData.commission !== undefined &&
                        pricingData.grossPrice !== undefined)
                    }
                    className={`w-full text-sm ${
                      pricingData.commission !== undefined &&
                      pricingData.grossPrice !== undefined
                        ? "bg-ui-bg-subtle cursor-not-allowed"
                        : showValidation && validationErrors.net_cost
                        ? "border-red-500 focus:border-red-500"
                        : ""
                    }`}
                  />
                  {showValidation && validationErrors.net_cost && (
                    <Text size="small" className="text-red-500">
                      {validationErrors.net_cost}
                    </Text>
                  )}
                </div>
              </td>
              <td className="border border-ui-border-base px-2 py-2">
                <div className="space-y-1">
                  <Input
                    id="marginRate"
                    type="number"
                    step="0.01"
                    min="0"
                    max="99.99"
                    placeholder="10"
                    value={
                      pricingData.marginRate !== undefined
                        ? (pricingData.marginRate * 100).toString()
                        : ""
                    }
                    onChange={(e) => {
                      const value =
                        e.target.value !== ""
                          ? parseFloat(e.target.value) / 100
                          : undefined;
                      handleInputChange("marginRate", value);
                    }}
                    disabled={disabled}
                    className={`w-full text-sm ${
                      showValidation && validationErrors.margin_rate
                        ? "border-red-500 focus:border-red-500"
                        : ""
                    }`}
                  />
                  {showValidation && validationErrors.margin_rate && (
                    <Text size="small" className="text-red-500">
                      {validationErrors.margin_rate}
                    </Text>
                  )}
                </div>
              </td>
              <td className="border border-ui-border-base px-2 py-2 bg-ui-bg-subtle">
                <Text weight="plus" size="small">
                  {(() => {
                    const value = calculations.calculatedSellingPrice;
                    if (value == null) return "—";
                    const numValue =
                      typeof value === "string" ? parseFloat(value) : value;
                    return isNaN(numValue) ? "—" : numValue.toFixed(2);
                  })()}
                </Text>
              </td>
              <td className="border border-ui-border-base px-2 py-2">
                <div className="space-y-1">
                  <Select
                    value={pricingData.sellingCurrency || ""}
                    onValueChange={(value) => {
                      const newValue = value || undefined;
                      setPricingData((prev) => ({
                        ...prev,
                        sellingCurrency: newValue,
                      }));
                    }}
                    disabled={disabled}
                  >
                    <Select.Trigger
                      className={`w-full text-sm ${
                        showValidation && validationErrors.selling_currency
                          ? "border-red-500 focus:border-red-500"
                          : ""
                      }`}
                    >
                      <Select.Value placeholder="Currency" />
                    </Select.Trigger>
                    <Select.Content>
                      {[
                        "USD",
                        "EUR",
                        "GBP",
                        "JPY",
                        "AUD",
                        "CAD",
                        "CHF",
                        "CNY",
                        "INR",
                      ].map((currency) => (
                        <Select.Item key={currency} value={currency}>
                          {currency}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                  {showValidation && validationErrors.selling_currency && (
                    <Text size="small" className="text-red-500">
                      {validationErrors.selling_currency}
                    </Text>
                  )}
                </div>
              </td>
              <td className="border border-ui-border-base px-2 py-2">
                <div className="space-y-1">
                  <Input
                    type="number"
                    step="0.000001"
                    min="0"
                    placeholder="1.0"
                    value={pricingData.exchangeRate?.toString() || ""}
                    onChange={(e) => {
                      const value = e.target.value
                        ? parseFloat(e.target.value)
                        : undefined;
                      handleInputChange("exchangeRate", value);
                    }}
                    disabled={disabled}
                    className={`w-full text-sm ${
                      showValidation && validationErrors.exchange_rate
                        ? "border-red-500 focus:border-red-500"
                        : ""
                    }`}
                  />
                  {showValidation && validationErrors.exchange_rate && (
                    <Text size="small" className="text-red-500">
                      {validationErrors.exchange_rate}
                    </Text>
                  )}
                </div>
              </td>
              <td className="border border-ui-border-base px-2 py-2 bg-ui-bg-subtle">
                <Text weight="plus" size="small">
                  {(() => {
                    const value =
                      calculations.calculatedSellingPriceSellingCurrency;
                    if (value == null) return "—";
                    const numValue =
                      typeof value === "string" ? parseFloat(value) : value;
                    return isNaN(numValue) ? "—" : numValue.toFixed(2);
                  })()}
                </Text>
              </td>
            </tr>

            {/* Addon Rows - Editable */}
            {addonLineItems.map((addon) => (
              <tr key={addon.id} className="">
                {/* Supplier Offering Selection (Combined Line Item + Supplier) */}
                <td className="border border-ui-border-base px-2 py-2">
                  {(() => {
                    // Get supplier offerings for this addon
                    const availableOfferings = addon.addon_id
                      ? getSupplierOfferingsForAddon(addon.addon_id)
                      : [];

                    // Check if we're still loading or if there are truly no offerings
                    const isLoading =
                      addon.addon_id &&
                      !addonSupplierOfferings.hasOwnProperty(addon.addon_id);
                    const hasNoOfferings =
                      availableOfferings.length === 0 && !isLoading;

                    return (
                      <div className="space-y-2">
                        {/* Addon name display */}
                        <div>
                          <Text weight="plus" size="small">
                            {addon.name}
                          </Text>
                          {addon.is_mandatory && (
                            <Text size="small" className="text-orange-600 mt-1">
                              Required
                            </Text>
                          )}
                        </div>

                        {/* Supplier offering dropdown */}
                        <Select
                          value={addon.supplier_offering_id || ""}
                          onValueChange={(value) => {
                            // Find the selected offering to extract supplier info
                            const selectedOffering = availableOfferings.find(
                              offering => offering.id === value
                            );

                            if (selectedOffering) {
                              // Update both supplier offering ID and supplier ID
                              handleAddonInputChange(addon.id, "supplier_offering_id", value);
                              handleAddonInputChange(addon.id, "supplier_id", selectedOffering.supplier_id);
                              handleAddonInputChange(addon.id, "supplier_offering_display_name", selectedOffering.display_name);
                            } else {
                              handleAddonInputChange(addon.id, "supplier_offering_id", undefined);
                              handleAddonInputChange(addon.id, "supplier_id", undefined);
                              handleAddonInputChange(addon.id, "supplier_offering_display_name", undefined);
                            }
                          }}
                          disabled={disabled || isLoading || hasNoOfferings}
                        >
                          <Select.Trigger className="w-full text-xs bg-white">
                            <Select.Value
                              placeholder={
                                isLoading
                                  ? "Loading supplier offerings..."
                                  : hasNoOfferings
                                  ? "No supplier offerings available"
                                  : "Select supplier offering"
                              }
                            />
                          </Select.Trigger>
                          <Select.Content>
                            {availableOfferings.map((offering) => (
                              <Select.Item key={offering.id} value={offering.id}>
                                <TruncatedSelectItem
                                  text={offering.display_name}
                                  maxLength={60}
                                />
                              </Select.Item>
                            ))}
                          </Select.Content>
                        </Select>
                      </div>
                    );
                  })()}
                </td>
                {/* Gross Price */}
                <td className="border border-ui-border-base px-2 py-2">
                  <Input
                    id={`addon-gross-price-${addon.id}`}
                    type="number"
                    value={addon.gross_price?.toString() || ""}
                    onChange={(e) =>
                      handleAddonInputChange(
                        addon.id,
                        "gross_price",
                        parseFloat(e.target.value) || undefined
                      )
                    }
                    placeholder="0.00"
                    disabled={disabled}
                    className="w-full text-sm bg-white"
                    step="0.01"
                    min="0"
                  />
                </td>
                <td className="border border-ui-border-base px-2 py-2">
                  <Input
                    id={`addon-commission-${addon.id}`}
                    type="number"
                    value={addon.commission?.toString() || ""}
                    onChange={(e) =>
                      handleAddonInputChange(
                        addon.id,
                        "commission",
                        parseFloat(e.target.value) || undefined
                      )
                    }
                    placeholder="0.00"
                    disabled={disabled}
                    className="w-full text-sm bg-white"
                    step="0.01"
                    min="0"
                    max="100"
                  />
                </td>
                <td className="border border-ui-border-base px-2 py-2">
                  <Input
                    id={`addon-net-cost-${addon.id}`}
                    type="number"
                    value={(() => {
                      // Priority: manual value first, then calculated value, then fallback to net_cost
                      if (addon.net_cost_manual !== undefined) {
                        return addon.net_cost_manual.toString();
                      }
                      if (addon.gross_price && addon.commission) {
                        const calculatedNetCost =
                          addon.gross_price -
                          addon.gross_price * (addon.commission / 100);
                        return calculatedNetCost.toString();
                      }
                      return addon.net_cost !== undefined
                        ? addon.net_cost.toString()
                        : "";
                    })()}
                    onChange={(e) =>
                      handleAddonInputChange(
                        addon.id,
                        "net_cost_manual",
                        parseFloat(e.target.value) || undefined
                      )
                    }
                    placeholder={(() => {
                      if (addon.gross_price && addon.commission) {
                        const calculatedNetCost =
                          addon.gross_price -
                          addon.gross_price * (addon.commission / 100);
                        return calculatedNetCost.toFixed(2);
                      }
                      return "0.00";
                    })()}
                    disabled={
                      disabled ||
                      (addon.gross_price !== undefined &&
                        addon.commission !== undefined)
                    }
                    className={`w-full text-sm ${
                      addon.gross_price !== undefined &&
                      addon.commission !== undefined
                        ? "bg-ui-bg-subtle cursor-not-allowed"
                        : "bg-white"
                    }`}
                    step="0.01"
                    min="0"
                  />
                </td>
                <td className="border border-ui-border-base px-2 py-2">
                  <Input
                    id={`addon-margin-rate-${addon.id}`}
                    type="number"
                    value={addon.margin_rate?.toString() || ""}
                    onChange={(e) =>
                      handleAddonInputChange(
                        addon.id,
                        "margin_rate",
                        parseFloat(e.target.value) || undefined
                      )
                    }
                    placeholder="0.00"
                    disabled={disabled}
                    className="w-full text-sm bg-white"
                    step="0.01"
                    min="0"
                    max="100"
                  />
                </td>
                <td className="border border-ui-border-base px-2 py-2">
                  <Text size="small" className="py-1">
                    {(() => {
                      // Calculate selling price: Net Cost ÷ (1 - Margin Rate)
                      let netCost: number | undefined;

                      // Use manual net cost if available, otherwise use calculated net cost
                      if (addon.net_cost_manual !== undefined) {
                        netCost = addon.net_cost_manual;
                      } else if (addon.gross_price && addon.commission) {
                        netCost =
                          addon.gross_price -
                          addon.gross_price * (addon.commission / 100);
                      } else {
                        netCost = addon.net_cost;
                      }

                      if (netCost && addon.margin_rate) {
                        const calculatedSellingPrice =
                          netCost / (1 - addon.margin_rate / 100);
                        return calculatedSellingPrice.toFixed(2);
                      }

                      return addon.selling_price !== undefined
                        ? addon.selling_price.toFixed(2)
                        : "—";
                    })()}
                  </Text>
                </td>
                <td className="border border-ui-border-base px-2 py-2">
                  <Select
                    value={addon.selling_currency || costCurrency}
                    onValueChange={(value) =>
                      handleAddonInputChange(
                        addon.id,
                        "selling_currency",
                        value
                      )
                    }
                    disabled={disabled}
                  >
                    <Select.Trigger className="w-full text-sm bg-white">
                      <Select.Value />
                    </Select.Trigger>
                    <Select.Content>
                      <Select.Item value="CHF">CHF</Select.Item>
                      <Select.Item value="EUR">EUR</Select.Item>
                      <Select.Item value="USD">USD</Select.Item>
                      <Select.Item value="GBP">GBP</Select.Item>
                    </Select.Content>
                  </Select>
                </td>
                <td className="border border-ui-border-base px-2 py-2">
                  <Input
                    id={`addon-exchange-rate-${addon.id}`}
                    type="number"
                    value={addon.exchange_rate?.toString() || ""}
                    onChange={(e) =>
                      handleAddonInputChange(
                        addon.id,
                        "exchange_rate",
                        parseFloat(e.target.value) || undefined
                      )
                    }
                    placeholder="1.0000"
                    disabled={disabled}
                    className="w-full text-sm bg-white"
                    step="0.0001"
                    min="0"
                  />
                </td>
                <td className="border border-ui-border-base px-2 py-2">
                  <Text size="small" className="py-1">
                    {(() => {
                      // Calculate selling price in selling currency
                      let sellingPrice: number | undefined;
                      let netCost: number | undefined;

                      // Use manual net cost if available, otherwise use calculated net cost
                      if (addon.net_cost_manual !== undefined) {
                        netCost = addon.net_cost_manual;
                      } else if (addon.gross_price && addon.commission) {
                        netCost =
                          addon.gross_price -
                          addon.gross_price * (addon.commission / 100);
                      } else {
                        netCost = addon.net_cost;
                      }

                      if (netCost && addon.margin_rate) {
                        sellingPrice = netCost / (1 - addon.margin_rate / 100);
                      } else {
                        sellingPrice = addon.selling_price;
                      }

                      if (sellingPrice && addon.exchange_rate) {
                        const convertedPrice =
                          sellingPrice * addon.exchange_rate;
                        return convertedPrice.toFixed(2);
                      }

                      return addon.selling_price_selling_currency !== undefined
                        ? addon.selling_price_selling_currency.toFixed(2)
                        : sellingPrice !== undefined
                        ? sellingPrice.toFixed(2)
                        : "—";
                    })()}
                  </Text>
                </td>
              </tr>
            ))}

            {/* Total Row - Only show when addons are present */}
            {addonLineItems.length > 0 && (
              <tr className="bg-ui-bg-base border-t-2 border-ui-border-strong">
                <td className="border border-ui-border-base px-2 py-3">
                  <Text weight="plus" size="small" className="text-ui-fg-base">
                    TOTAL
                  </Text>
                </td>
                <td className="border border-ui-border-base px-2 py-3"></td>
                <td className="border border-ui-border-base px-2 py-3"></td>
                <td className="border border-ui-border-base px-2 py-3"></td>
                <td className="border border-ui-border-base px-2 py-3"></td>
                <td className="border border-ui-border-base px-2 py-3">
                  {/* <Text weight="plus" size="small" className="text-ui-fg-base">
                    {(() => {
                      // Calculate main selling price
                      const mainSellingPrice =
                        calculations.calculatedSellingPrice ||
                        pricingData.sellingPrice ||
                        0;

                      // Calculate addons total by summing individual addon selling prices using same logic as display
                      const addonsTotal = addonLineItems.reduce(
                        (sum, addon) => {
                          let sellingPrice: number | undefined;
                          let netCost: number | undefined;

                          // Use manual net cost if available, otherwise use calculated net cost
                          if (addon.net_cost_manual !== undefined) {
                            netCost = addon.net_cost_manual;
                          } else if (addon.gross_price && addon.commission) {
                            netCost =
                              addon.gross_price -
                              addon.gross_price * (addon.commission / 100);
                          } else {
                            netCost = addon.net_cost;
                          }

                          if (netCost && addon.margin_rate) {
                            sellingPrice =
                              netCost / (1 - addon.margin_rate / 100);
                          } else {
                            sellingPrice = addon.selling_price;
                          }

                          return sum + (sellingPrice || 0);
                        },
                        0
                      );

                      const total = mainSellingPrice + addonsTotal;
                      return `${total.toFixed(2)}`;
                    })()}
                  </Text> */}
                </td>
                <td className="border border-ui-border-base px-2 py-3">

                </td>
                <td className="border border-ui-border-base px-2 py-3"></td>
                <td className="border border-ui-border-base px-2 py-3">
                  <Text weight="plus" size="small" className="text-ui-fg-base">
                    {(() => {
                      // Calculate main selling price in selling currency
                      const mainSellingPriceSellingCurrency =
                        calculations.calculatedSellingPriceSellingCurrency ||
                        pricingData.sellingPriceSellingCurrency ||
                        0;

                      // Calculate addons total in selling currency using same logic as display
                      const addonsSellingCurrencyTotal = addonLineItems.reduce(
                        (sum, addon) => {
                          let sellingPrice: number | undefined;
                          let netCost: number | undefined;

                          // Use manual net cost if available, otherwise use calculated net cost
                          if (addon.net_cost_manual !== undefined) {
                            netCost = addon.net_cost_manual;
                          } else if (addon.gross_price && addon.commission) {
                            netCost =
                              addon.gross_price -
                              addon.gross_price * (addon.commission / 100);
                          } else {
                            netCost = addon.net_cost;
                          }

                          if (netCost && addon.margin_rate) {
                            sellingPrice =
                              netCost / (1 - addon.margin_rate / 100);
                          } else {
                            sellingPrice = addon.selling_price;
                          }

                          // Calculate selling price in selling currency
                          let sellingPriceInSellingCurrency: number | undefined;
                          if (sellingPrice && addon.exchange_rate) {
                            sellingPriceInSellingCurrency =
                              sellingPrice * addon.exchange_rate;
                          } else {
                            sellingPriceInSellingCurrency =
                              addon.selling_price_selling_currency;
                          }

                          return sum + (sellingPriceInSellingCurrency || 0);
                        },
                        0
                      );

                      if (
                        pricingData.sellingCurrency &&
                        (mainSellingPriceSellingCurrency > 0 ||
                          addonsSellingCurrencyTotal > 0)
                      ) {
                        const total =
                          mainSellingPriceSellingCurrency +
                          addonsSellingCurrencyTotal;
                        return `${total.toFixed(2)}`;
                      }
                      return "—";
                    })()}
                  </Text>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Errors */}
      {errors.length > 0 && (
        <div className="space-y-2">
          <Text weight="plus" className="text-ui-fg-error">
            Pricing Errors:
          </Text>
          {errors.map((error, index) => (
            <Text key={index} size="small" className="text-ui-fg-error">
              • {error}
            </Text>
          ))}
        </div>
      )}

      {/* Addon Selection Modal */}
      {showAddonModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[85vh] overflow-hidden">
            <div className="p-8 border-b border-ui-border-base">
              <Text weight="plus" size="large" className="text-xl">
                Select Add-ons
              </Text>
              <Text size="small" className="text-ui-fg-subtle mt-2">
                Choose add-ons to add to the pricing calculator. These will appear as additional rows in the pricing table.
              </Text>
            </div>

            <div className="p-8 min-h-[300px] max-h-[50vh] overflow-y-auto">
              <MultiSelect
                options={availableAddons}
                selectedValues={selectedAddonIds}
                onChange={setSelectedAddonIds}
                placeholder="Select add-ons to add..."
                showSelectAll={true}
                showSelectedTags={true}
                maxHeight="max-h-64"
              />

              {availableAddons.length === 0 && (
                <div className="text-center py-8 text-ui-fg-subtle">
                  <Text>No add-ons available from custom fields</Text>
                </div>
              )}
            </div>

            <div className="p-8 border-t border-ui-border-base flex justify-end gap-4">
              <Button
                type="button"
                variant="secondary"
                onClick={handleAddonModalCancel}
                size="base"
              >
                Cancel
              </Button>
              <Button
                type="button"
                onClick={handleAddonModalSave}
                disabled={selectedAddonIds.length === 0}
                size="base"
              >
                Add Selected ({selectedAddonIds.length})
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PricingCalculator;

import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";

import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";
import { CreateProductServiceInput, ProductServiceOutput, UpdateProductServiceInput } from "src/modules/supplier-products-services/types";
import { generateProductServiceName } from "src/modules/supplier-products-services/utils/name-generator";

export type BulkImportProductServicesStepInput = {
  product_services: CreateProductServiceInput[];
};

type BulkImportProductServicesWorkflowInput = BulkImportProductServicesStepInput;

export interface BulkImportProductServicesResult {
  imported: number;
  updated: number;
  errors: Array<{
    index: number;
    name: string;
    error: string;
  }>;
  product_services: ProductServiceOutput[];
}

// Step to validate products/services before import
const validateProductServicesStep = createStep(
  "validate-product-services-step",
  async (input: BulkImportProductServicesStepInput, { container }) => {
    console.log("ValidateProductServicesStep - Input:", JSON.stringify(input, null, 2));

    const { product_services } = input;
    const errors: Array<{ index: number; name: string; error: string }> = [];

    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      container.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    // Validate each product/service
    for (let i = 0; i < product_services.length; i++) {
      const productService = product_services[i];
      const name = productService.name || 'Unknown';

      // Validate required fields
      if (!productService.type || !['Product', 'Service'].includes(productService.type)) {
        errors.push({
          index: i,
          name,
          error: 'Type must be either "Product" or "Service"'
        });
      }

      if (!productService.category_id) {
        errors.push({
          index: i,
          name,
          error: 'Category ID is required'
        });
      } else {
        // Validate category exists
        try {
          const categories = await supplierProductsServicesService.listCategories(
            { id: productService.category_id },
            { skip: 0, take: 1 }
          );
          if (categories.length === 0) {
            errors.push({
              index: i,
              name,
              error: `Category with ID ${productService.category_id} does not exist`
            });
          }
        } catch (error) {
          errors.push({
            index: i,
            name,
            error: `Failed to validate category: ${error instanceof Error ? error.message : 'Unknown error'}`
          });
        }
      }

      if (!productService.unit_type_id) {
        errors.push({
          index: i,
          name,
          error: 'Unit Type ID is required'
        });
      } else {
        // Validate unit type exists
        try {
          const unitTypes = await supplierProductsServicesService.listUnitTypes(
            { id: productService.unit_type_id },
            { skip: 0, take: 1 }
          );
          if (unitTypes.length === 0) {
            errors.push({
              index: i,
              name,
              error: `Unit Type with ID ${productService.unit_type_id} does not exist`
            });
          }
        } catch (error) {
          errors.push({
            index: i,
            name,
            error: `Failed to validate unit type: ${error instanceof Error ? error.message : 'Unknown error'}`
          });
        }
      }

      // Validate tag IDs if provided
      if (productService.tag_ids && productService.tag_ids.length > 0) {
        try {
          const tags = await supplierProductsServicesService.listTags({}, { skip: 0, take: 100 });
          const existingTagIds = tags.map(tag => tag.id);
          const invalidTagIds = productService.tag_ids.filter(tagId => !existingTagIds.includes(tagId));
          
          if (invalidTagIds.length > 0) {
            errors.push({
              index: i,
              name,
              error: `Invalid tag IDs: ${invalidTagIds.join(', ')}`
            });
          }
        } catch (error) {
          errors.push({
            index: i,
            name,
            error: `Failed to validate tags: ${error instanceof Error ? error.message : 'Unknown error'}`
          });
        }
      }

      // Validate custom fields against category schema
      if (productService.custom_fields && productService.category_id) {
        try {
          const categories = await supplierProductsServicesService.listCategories(
            { id: productService.category_id },
            { skip: 0, take: 1 }
          );

          if (categories.length > 0) {
            const category = categories[0];

            if (Array.isArray(category.dynamic_field_schema)) {
              // Check required fields that are used in products
              const requiredFields = category.dynamic_field_schema.filter((field: any) =>
                field.required && field.used_in_product !== false
              );

              for (const field of requiredFields) {
                const fieldValue = productService.custom_fields[field.key];

                // Skip validation for certain fields that can be auto-generated or are optional for import
                const skipValidationFields = ['name']; // Fields that can be null during import
                if (skipValidationFields.includes(field.key)) {
                  continue;
                }

                const isEmpty = !fieldValue || fieldValue.toString().trim() === '';

                if (isEmpty) {
                  errors.push({
                    index: i,
                    name,
                    error: `${field.label || field.key} is required`
                  });
                }
              }
            }
          }
        } catch (error) {
          errors.push({
            index: i,
            name,
            error: `Failed to validate custom fields: ${error instanceof Error ? error.message : 'Unknown error'}`
          });
        }
      }
    }

    console.log("ValidateProductServicesStep - Validation errors:", errors);

    return new StepResponse(
      { product_services, validationErrors: errors },
      errors.length > 0 ? errors : undefined
    );
  }
);

// Step to bulk import products/services
const bulkImportProductServicesStep = createStep(
  "bulk-import-product-services-step",
  async (
    input: { product_services: CreateProductServiceInput[]; validationErrors: Array<{ index: number; name: string; error: string }> },
    { container }
  ) => {
    console.log("BulkImportProductServicesStep - Starting import for products/services:", input.product_services.length);

    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      container.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    // Get query service for hotel/destination name resolution
    const queryService = container.resolve("query");


    const result: BulkImportProductServicesResult = {
      imported: 0,
      updated: 0,
      errors: [...input.validationErrors],
      product_services: [],
    };

    // If there are validation errors, don't proceed with import
    if (input.validationErrors.length > 0) {
      console.log("BulkImportProductServicesStep - Skipping import due to validation errors");
      return new StepResponse(result, []);
    }

    // Import products/services one by one
    for (let i = 0; i < input.product_services.length; i++) {
      const productServiceData = input.product_services[i];

      try {
        console.log(`BulkImportProductServicesStep - Processing product/service ${i + 1}/${input.product_services.length}`);

        // Get category to access dynamic field schema and generate name
        const categories = await supplierProductsServicesService.listCategories(
          { id: productServiceData.category_id },
          { skip: 0, take: 1 }
        );

        if (categories.length === 0) {
          result.errors.push({
            index: i,
            name: 'Unknown',
            error: `Category with ID ${productServiceData.category_id} not found`
          });
          continue;
        }

        const category = categories[0];
        const dynamicFieldSchema = Array.isArray(category.dynamic_field_schema) ? category.dynamic_field_schema : [];

        // Generate name using the existing name generator utility with query service for name resolution
        const generatedName = await generateProductServiceName(
          category.name,
          productServiceData.custom_fields || {},
          dynamicFieldSchema,
          queryService
        );

        console.log(`Generated name: ${generatedName}`);

        // Check if a product/service with this generated name already exists
        const existingProductServices = await supplierProductsServicesService.listProductServicesWithFilters({
          category_id: productServiceData.category_id,
        });

        const existingProduct = existingProductServices.find(existing => existing.name === generatedName);

        let finalProductService: ProductServiceOutput;

        if (existingProduct) {
          // Update existing product/service
          console.log(`BulkImportProductServicesStep - Updating existing product/service: ${generatedName}`);

          const updateData: UpdateProductServiceInput = {
            type: productServiceData.type,
            description: productServiceData.description,
            base_cost: productServiceData.base_cost,
            custom_fields: productServiceData.custom_fields,
            status: productServiceData.status || "active",
            category_id: productServiceData.category_id,
            unit_type_id: productServiceData.unit_type_id,
            service_level: productServiceData.service_level || "hotel",
            hotel_id: productServiceData.hotel_id,
            destination_id: productServiceData.destination_id,
            tag_ids: productServiceData.tag_ids,
          };

          finalProductService = await supplierProductsServicesService.updateProductService(
            existingProduct.id,
            updateData
          );

          result.updated++;
          console.log(`BulkImportProductServicesStep - Successfully updated product/service: ${finalProductService.name}`);
        } else {
          // Create new product/service
          console.log(`BulkImportProductServicesStep - Creating new product/service: ${generatedName}`);

          const createData: CreateProductServiceInput = {
            name: generatedName, // Use the generated name
            type: productServiceData.type,
            description: productServiceData.description,
            base_cost: productServiceData.base_cost,
            custom_fields: productServiceData.custom_fields,
            status: productServiceData.status || "active",
            category_id: productServiceData.category_id,
            unit_type_id: productServiceData.unit_type_id,
            service_level: productServiceData.service_level || "hotel",
            hotel_id: productServiceData.hotel_id,
            destination_id: productServiceData.destination_id,
            tag_ids: productServiceData.tag_ids,
          };

          finalProductService = await supplierProductsServicesService.createProductService(createData, queryService);

          result.imported++;
          console.log(`BulkImportProductServicesStep - Successfully created product/service: ${finalProductService.name}`);
        }

        result.product_services.push(finalProductService);
        
      } catch (error) {
        console.error(`BulkImportProductServicesStep - Failed to process product/service:`, error);

        result.errors.push({
          index: i,
          name: 'Auto-generated name',
          error: error instanceof Error ? error.message : 'Unknown error occurred'
        });
      }
    }

    console.log("BulkImportProductServicesStep - Import completed:", {
      imported: result.imported,
      updated: result.updated,
      errors: result.errors.length,
      total: input.product_services.length
    });

    return new StepResponse(result, result.product_services.map(ps => ps.id));
  },
  async (createdProductServiceIds: string[], { container }) => {
    console.log("BulkImportProductServicesStep - Compensating for products/services:", createdProductServiceIds);

    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      container.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    // Delete any products/services that were created during this import
    for (const productServiceId of createdProductServiceIds) {
      try {
        await supplierProductsServicesService.deleteProductService(productServiceId);
        console.log(`BulkImportProductServicesStep - Compensated product/service: ${productServiceId}`);
      } catch (error) {
        console.error(`Failed to compensate product/service ${productServiceId}:`, error);
      }
    }
  }
);

export const BulkImportProductServicesWorkflow = createWorkflow(
  "bulk-import-product-services",
  (input: BulkImportProductServicesWorkflowInput) => {
    const validatedInput = validateProductServicesStep(input);
    const result = bulkImportProductServicesStep(validatedInput);

    return new WorkflowResponse(result);
  }
);

import { MedusaService } from "@camped-ai/framework/utils";
import { MedusaContainer } from "@camped-ai/framework/types";
import BookingAddOn from "./models/booking-add-on";
import {
  CreateBookingAddOnInput,
  UpdateBookingAddOnInput,
  BookingAddOnResponse,
  AvailableAddOnResponse,
  CustomerFieldValidationResult,
  AvailableAddOnsFilters,
  ListAvailableAddOnsResponse,
  ListBookingAddOnsResponse,
} from "./types";

class BookingAddOnService extends MedusaService({
  BookingAddOn,
}) {
  protected container: MedusaContainer;

  constructor(container: MedusaContainer) {
    super(container);
    this.container = container;
  }

  /**
   * Get all available add-ons (no filtering for now)
   */
  async getAvailableAddOns(
    filters?: AvailableAddOnsFilters
  ): Promise<ListAvailableAddOnsResponse> {
    try {
      console.log("🔍 Fetching all available add-ons...");

      const query = this.container.resolve("query");

      const { data: addOns } = await query.graph({
        entity: "product_variant",
        filters: {
          metadata: {
            add_on_service: true,
            status: "Active",
          },
        },
        fields: ["id", "title", "metadata", "created_at", "updated_at"],
      });

      // Transform to include pricing and customer fields
      const transformedAddOns: AvailableAddOnResponse[] = addOns.map(
        (addOn: any) => ({
          id: addOn.id,
          title: addOn.title,
          description: addOn.metadata?.description || "",
          selling_price: parseFloat(addOn.metadata?.selling_price) || 0,
          selling_currency: addOn.metadata?.selling_currency || "CHF",
          service_level: addOn.metadata?.service_level || "hotel",
          customer_fields: addOn.metadata?.customer_fields || [],
          metadata: addOn.metadata,
        })
      );

      console.log(`✅ Found ${transformedAddOns.length} available add-ons`);

      return {
        add_ons: transformedAddOns,
        count: transformedAddOns.length,
      };
    } catch (error) {
      console.error("❌ Failed to fetch available add-ons:", error);
      throw error;
    }
  }

  /**
   * Create a booking add-on entry
   */
  async createBookingAddOn(
    data: CreateBookingAddOnInput
  ): Promise<BookingAddOnResponse> {
    try {
      console.log(`📝 Creating booking add-on for order ${data.order_id}...`);

      const bookingAddOnData = {
        order_id: data.order_id,
        add_on_variant_id: data.add_on_variant_id,
        add_on_name: data.add_on_name,
        quantity: data.quantity || 1,
        unit_price: data.unit_price,
        total_price: (data.quantity || 1) * data.unit_price,
        currency_code: data.currency_code || "CHF",
        customer_field_responses: data.customer_field_responses || {},
        add_on_metadata: data.add_on_metadata || {},
      };

      // Use the generated createBookingAddOns method from MedusaService
      const createdBookingAddOns = await this.createBookingAddOns([
        bookingAddOnData,
      ]);
      const savedBookingAddOn = createdBookingAddOns[0];

      console.log("✅ Booking add-on saved to database:", savedBookingAddOn.id);
      console.log("🔍 Saved booking add-on structure:", JSON.stringify(savedBookingAddOn, null, 2));

      return savedBookingAddOn as BookingAddOnResponse;
    } catch (error) {
      console.error("❌ Failed to create booking add-on:", error);
      throw error;
    }
  }

  /**
   * Get booking add-ons for an order
   */
  async getBookingAddOns(orderId: string): Promise<ListBookingAddOnsResponse> {
    try {
      console.log(`🔍 Fetching booking add-ons for order ${orderId}...`);

      // Use the generated listBookingAddOns method with filters
      const bookingAddOns = await this.listBookingAddOns({
        order_id: orderId,
      });

      return {
        booking_add_ons: bookingAddOns as BookingAddOnResponse[],
        count: bookingAddOns.length,
      };
    } catch (error) {
      console.error("❌ Failed to fetch booking add-ons:", error);
      throw error;
    }
  }

  /**
   * Get all booking add-ons with related data
   */
  async getAllBookingAddOns(filters?: {
    limit?: number;
    offset?: number;
    order_id?: string;
  }): Promise<ListBookingAddOnsResponse> {
    try {
      console.log("🔍 Fetching all booking add-ons with related data...");

      const { limit = 50, offset = 0, order_id } = filters || {};

      // Build filters for the query
      const queryFilters: any = {};
      if (order_id) {
        queryFilters.order_id = order_id;
      }

      // Use the generated listBookingAddOns method
      const bookingAddOns = await this.listBookingAddOns(queryFilters, {
        skip: offset,
        take: limit,
      });

      // Enhance with related data
      const enhancedAddOns = await Promise.all(
        bookingAddOns.map(async (addon: any) => {
          // Fetch order data - for now, we'll skip the related data fetching
          // and focus on getting the basic booking add-on data working
          let orderData = null;
          if (addon.order_id) {
            console.log(
              `🔍 Order ID found: ${addon.order_id} (related data fetch skipped for now)`
            );
            // TODO: Implement proper order data fetching once container resolution is fixed
          }

          // Fetch add-on variant data - for now, we'll skip the related data fetching
          let addOnData = null;
          if (addon.add_on_variant_id) {
            console.log(
              `🔍 Variant ID found: ${addon.add_on_variant_id} (related data fetch skipped for now)`
            );
            // TODO: Implement proper variant data fetching once container resolution is fixed
          }

          return {
            ...addon,
            order: orderData,
            add_on: addOnData
              ? {
                  id: addOnData.id,
                  name: addOnData.title,
                  metadata: addOnData.metadata || {},
                }
              : null,
          };
        })
      );

      console.log(
        `✅ Found ${enhancedAddOns.length} booking add-ons with related data`
      );

      return {
        booking_add_ons: enhancedAddOns as BookingAddOnResponse[],
        count: enhancedAddOns.length,
      };
    } catch (error) {
      console.error("❌ Failed to fetch all booking add-ons:", error);
      throw error;
    }
  }

  /**
   * Update a booking add-on
   */
  async updateBookingAddOn(
    id: string,
    data: UpdateBookingAddOnInput
  ): Promise<BookingAddOnResponse> {
    try {
      console.log(`📝 Updating booking add-on ${id}...`);
      console.log("📊 Update data:", data);

      // First, get the existing booking add-on using the MedusaService method
      const existingAddOn = await this.retrieveBookingAddOn(id);

      if (!existingAddOn) {
        throw new Error(`Booking add-on with ID ${id} not found`);
      }

      // Prepare update data
      const updateData: any = {
        updated_at: new Date(),
      };

      if (data.quantity !== undefined) {
        updateData.quantity = data.quantity;
        // Recalculate total price if quantity changes
        updateData.total_price = data.quantity * existingAddOn.unit_price;
      }

      if (data.unit_price !== undefined) {
        updateData.unit_price = data.unit_price;
        // Recalculate total price if unit price changes
        const quantity =
          data.quantity !== undefined ? data.quantity : existingAddOn.quantity;
        updateData.total_price = quantity * data.unit_price;
      }

      if (data.currency_code !== undefined) {
        updateData.currency_code = data.currency_code;
      }

      if (data.customer_field_responses !== undefined) {
        updateData.customer_field_responses = JSON.stringify(
          data.customer_field_responses
        );
      }

      if (data.supplier_order_id !== undefined) {
        updateData.supplier_order_id = data.supplier_order_id;
      }

      if (data.order_status !== undefined) {
        updateData.order_status = data.order_status;
      }

      // Update the booking add-on using MedusaService method with correct selector pattern
      const updatedAddOns = await this.updateBookingAddOns({
        selector: { id: id },
        data: updateData,
      });
      const updatedAddOn = Array.isArray(updatedAddOns)
        ? updatedAddOns[0]
        : updatedAddOns;

      if (!updatedAddOn) {
        throw new Error(`Failed to update booking add-on ${id}`);
      }

      console.log(`✅ Successfully updated booking add-on ${id}`);

      // Transform to response format
      return {
        id: updatedAddOn.id,
        order_id: updatedAddOn.order_id,
        add_on_variant_id: updatedAddOn.add_on_variant_id,
        add_on_name: updatedAddOn.add_on_name,
        quantity: updatedAddOn.quantity,
        unit_price: updatedAddOn.unit_price,
        total_price: updatedAddOn.total_price,
        currency_code: updatedAddOn.currency_code,
        customer_field_responses: updatedAddOn.customer_field_responses
          ? typeof updatedAddOn.customer_field_responses === "string"
            ? JSON.parse(updatedAddOn.customer_field_responses)
            : updatedAddOn.customer_field_responses
          : {},
        add_on_metadata: updatedAddOn.add_on_metadata
          ? typeof updatedAddOn.add_on_metadata === "string"
            ? JSON.parse(updatedAddOn.add_on_metadata)
            : updatedAddOn.add_on_metadata
          : {},
        supplier_order_id: updatedAddOn.supplier_order_id,
        order_status: updatedAddOn.order_status as any,
        created_at: updatedAddOn.created_at,
        updated_at: updatedAddOn.updated_at,
      };
    } catch (error) {
      console.error("❌ Failed to update booking add-on:", error);
      throw error;
    }
  }

  /**
   * Delete a booking add-on
   */
  async deleteBookingAddOn(id: string): Promise<void> {
    try {
      console.log(`🗑️ Deleting booking add-on ${id}...`);

      // For now, just log the deletion
      console.log("✅ Booking add-on deleted");
    } catch (error) {
      console.error("❌ Failed to delete booking add-on:", error);
      throw error;
    }
  }

  /**
   * Validate customer field responses
   */
  async validateCustomerFields(
    addOnId: string,
    responses: Record<string, any>
  ): Promise<CustomerFieldValidationResult> {
    try {
      console.log(`🔍 Validating customer fields for add-on ${addOnId}...`);

      // For now, return valid
      // Later we'll implement proper validation based on field schema
      return {
        valid: true,
        errors: [],
        field_errors: {},
      };
    } catch (error) {
      console.error("❌ Failed to validate customer fields:", error);
      return {
        valid: false,
        errors: ["Validation failed"],
        field_errors: {},
      };
    }
  }
}

export default BookingAddOnService;

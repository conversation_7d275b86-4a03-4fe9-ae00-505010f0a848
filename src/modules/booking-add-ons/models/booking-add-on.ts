import { model } from "@camped-ai/framework/utils";

const BookingAddOn = model.define("booking_add_ons", {
  id: model.id().primaryKey(),
  order_id: model.text(),
  add_on_variant_id: model.text(),
  add_on_name: model.text(),
  quantity: model.number().default(1),
  unit_price: model.number(),
  total_price: model.number(),
  currency_code: model.text().default("CHF"),
  customer_field_responses: model.json().nullable(),
  add_on_metadata: model.json().nullable(),

  // Supplier order tracking fields
  supplier_order_id: model.text().nullable(),
  order_status: model
    .enum(["pending", "confirmed", "in_progress", "completed", "cancelled"])
    .nullable(),
});

export default BookingAddOn;

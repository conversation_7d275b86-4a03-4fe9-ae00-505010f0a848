import {
  generateProductServiceName,
  generateProductServiceUniqueKey,
  generateSupplierOfferingUniqueKey,
} from "../name-generator";
import { DynamicFieldSchema } from "../../types";

describe("Name Generator Utilities", () => {
  const mockDynamicFieldSchema: DynamicFieldSchema[] = [
    {
      label: "Vehicle Type",
      key: "vehicle_type",
      type: "dropdown",
      options: ["Sedan", "Minivan", "Bus"],
      required: true,
      used_in_product: true,
      used_in_supplier_offering: true,
    },
    {
      label: "Passenger Count",
      key: "passenger_count",
      type: "number",
      required: true,
      used_in_product: true,
      used_in_supplier_offering: false,
    },
    {
      label: "Optional Notes",
      key: "notes",
      type: "text",
      required: false,
      used_in_product: false,
      used_in_supplier_offering: false,
    },
    {
      label: "Age Range",
      key: "age_range",
      type: "number-range",
      required: true,
      used_in_product: true,
      used_in_supplier_offering: true,
    },
  ];

  describe("generateProductServiceName", () => {
    it("should generate name with category only when no custom fields", async () => {
      const result = await generateProductServiceName("Taxi Transfer", {}, []);
      expect(result).toBe("Taxi Transfer");
    });

    it("should generate name with category and fields marked for product naming", async () => {
      const customFields = {
        vehicle_type: "Minivan",
        passenger_count: 6,
        age_range: { min: 18, max: 65 },
        notes: "This should not appear",
      };

      const result = await generateProductServiceName(
        "Taxi Transfer",
        customFields,
        mockDynamicFieldSchema
      );

      expect(result).toBe("Taxi Transfer – 18-65 – 6 – Minivan");
    });

    it("should handle missing fields gracefully", async () => {
      const customFields = {
        vehicle_type: "Minivan",
        // passenger_count missing
        age_range: { min: 18, max: 65 },
      };

      const result = await generateProductServiceName(
        "Taxi Transfer",
        customFields,
        mockDynamicFieldSchema
      );

      expect(result).toBe("Taxi Transfer – 18-65 – Minivan");
    });

    it("should handle boolean fields", async () => {
      const schema: DynamicFieldSchema[] = [
        {
          label: "Has WiFi",
          key: "has_wifi",
          type: "boolean",
          required: true,
          used_in_product: true,
        },
      ];

      const customFields = { has_wifi: true };
      const result = await generateProductServiceName(
        "Hotel Room",
        customFields,
        schema
      );
      expect(result).toBe("Hotel Room – Yes");
    });

    it("should include optional fields marked for product naming", async () => {
      const schema: DynamicFieldSchema[] = [
        {
          label: "Vehicle Type",
          key: "vehicle_type",
          type: "dropdown",
          options: ["Sedan", "Minivan"],
          required: false, // Not required but used in product
          used_in_product: true,
        },
        {
          label: "Notes",
          key: "notes",
          type: "text",
          required: false,
          used_in_product: false, // Not used in product
        },
      ];

      const customFields = {
        vehicle_type: "Sedan",
        notes: "This should not appear",
      };

      const result = await generateProductServiceName(
        "Transport",
        customFields,
        schema
      );
      expect(result).toBe("Transport – Sedan");
    });

    it("should respect field ordering when specified", async () => {
      const schema: DynamicFieldSchema[] = [
        {
          label: "Vehicle Type",
          key: "vehicle_type",
          type: "dropdown",
          options: ["Sedan", "Minivan"],
          required: true,
          used_in_product: true,
          order: 2, // Should appear second
        },
        {
          label: "Passenger Count",
          key: "passenger_count",
          type: "number",
          required: true,
          used_in_product: true,
          order: 1, // Should appear first
        },
      ];

      const customFields = {
        vehicle_type: "Minivan",
        passenger_count: 6,
      };

      const result = await generateProductServiceName(
        "Transport",
        customFields,
        schema
      );
      expect(result).toBe("Transport – 6 – Minivan");
    });

    it("should handle mixed ordering (some with order, some without)", async () => {
      const schema: DynamicFieldSchema[] = [
        {
          label: "Vehicle Type",
          key: "vehicle_type",
          type: "dropdown",
          options: ["Sedan", "Minivan"],
          required: true,
          used_in_product: true,
          order: 1, // Has order - should appear first
        },
        {
          label: "Passenger Count",
          key: "passenger_count",
          type: "number",
          required: true,
          used_in_product: true,
          // No order - should appear after ordered fields, alphabetically
        },
        {
          label: "Age Range",
          key: "age_range",
          type: "number-range",
          required: true,
          used_in_product: true,
          // No order - should appear after ordered fields, alphabetically
        },
      ];

      const customFields = {
        vehicle_type: "Minivan",
        passenger_count: 6,
        age_range: { min: 18, max: 65 },
      };

      const result = await generateProductServiceName(
        "Transport",
        customFields,
        schema
      );
      expect(result).toBe("Transport – Minivan – 18-65 – 6");
    });

    it("should handle hotel fields with query service", async () => {
      const schema: DynamicFieldSchema[] = [
        {
          label: "Hotels",
          key: "hotels",
          type: "hotels",
          required: true,
          used_in_product: true,
        },
      ];

      const mockQueryService = {
        graph: jest.fn().mockResolvedValue({
          data: [{ id: "hotel-1", name: "Grand Hotel" }],
        }),
      };

      const customFields = { hotels: ["hotel-1"] };
      const result = await generateProductServiceName(
        "Transfer",
        customFields,
        schema,
        mockQueryService
      );
      expect(result).toBe("Transfer – Grand Hotel");
    });

    it("should handle destination fields with query service", async () => {
      const schema: DynamicFieldSchema[] = [
        {
          label: "Destinations",
          key: "destinations",
          type: "destinations",
          required: true,
          used_in_product: true,
        },
      ];

      const mockQueryService = {
        graph: jest.fn().mockResolvedValue({
          data: [{ id: "dest-1", name: "Zurich Airport" }],
        }),
      };

      const customFields = { destinations: ["dest-1"] };
      const result = await generateProductServiceName(
        "Transfer",
        customFields,
        schema,
        mockQueryService
      );
      expect(result).toBe("Transfer – Zurich Airport");
    });

    it("should fallback to IDs when hotel resolution fails", async () => {
      const schema: DynamicFieldSchema[] = [
        {
          label: "Hotels",
          key: "hotels",
          type: "hotels",
          required: true,
          used_in_product: true,
        },
      ];

      const mockQueryService = {
        graph: jest.fn().mockRejectedValue(new Error("Hotel not found")),
      };

      const customFields = { hotels: ["hotel-1"] };
      const result = await generateProductServiceName(
        "Transfer",
        customFields,
        schema,
        mockQueryService
      );
      expect(result).toBe("Transfer – hotel-1");
    });

    it("should handle hotel fields without query service", async () => {
      const schema: DynamicFieldSchema[] = [
        {
          label: "Hotels",
          key: "hotels",
          type: "hotels",
          required: true,
          used_in_product: true,
        },
      ];

      const customFields = { hotels: ["hotel-1", "hotel-2"] };
      const result = await generateProductServiceName(
        "Transfer",
        customFields,
        schema
      );
      expect(result).toBe("Transfer – hotel-1, hotel-2");
    });
  });

  describe("generateProductServiceUniqueKey", () => {
    it("should generate consistent unique keys", () => {
      const customFields = {
        vehicle_type: "Minivan",
        passenger_count: 6,
        age_range: { min: 18, max: 65 },
      };

      const key1 = generateProductServiceUniqueKey(
        "cat_123",
        customFields,
        mockDynamicFieldSchema
      );
      const key2 = generateProductServiceUniqueKey(
        "cat_123",
        customFields,
        mockDynamicFieldSchema
      );

      expect(key1).toBe(key2);
      expect(key1).toContain("cat_123");
      expect(key1).toContain("vehicle_type:minivan");
      expect(key1).toContain("passenger_count:6");
      expect(key1).toContain("age_range:18:65");
    });

    it("should generate different keys for different values", () => {
      const customFields1 = { vehicle_type: "Sedan", passenger_count: 4 };
      const customFields2 = { vehicle_type: "Minivan", passenger_count: 6 };

      const key1 = generateProductServiceUniqueKey(
        "cat_123",
        customFields1,
        mockDynamicFieldSchema
      );
      const key2 = generateProductServiceUniqueKey(
        "cat_123",
        customFields2,
        mockDynamicFieldSchema
      );

      expect(key1).not.toBe(key2);
    });
  });

  describe("generateSupplierOfferingUniqueKey", () => {
    it("should include supplier, product, and date range in key", () => {
      const customFields = {
        vehicle_type: "Minivan",
        age_range: { min: 18, max: 65 },
      };

      const key = generateSupplierOfferingUniqueKey(
        "supplier_123",
        "product_456",
        new Date("2024-01-01"),
        new Date("2024-12-31"),
        customFields,
        mockDynamicFieldSchema
      );

      expect(key).toContain("supplier_123");
      expect(key).toContain("product_456");
      expect(key).toContain("2024-01-01:2024-12-31");
      expect(key).toContain("vehicle_type:minivan");
      expect(key).toContain("age_range:18:65");
    });

    it("should handle missing dates", () => {
      const key = generateSupplierOfferingUniqueKey(
        "supplier_123",
        "product_456",
        undefined,
        undefined,
        {},
        []
      );

      expect(key).toContain("__NO_FROM__:__NO_TO__");
    });

    it("should only include fields marked for supplier offerings", () => {
      const customFields = {
        vehicle_type: "Minivan", // used_in_supplier_offering: true
        passenger_count: 6, // used_in_supplier_offering: false
        age_range: { min: 18, max: 65 }, // used_in_supplier_offering: true
      };

      const key = generateSupplierOfferingUniqueKey(
        "supplier_123",
        "product_456",
        undefined,
        undefined,
        customFields,
        mockDynamicFieldSchema
      );

      expect(key).toContain("vehicle_type:minivan");
      expect(key).toContain("age_range:18:65");
      expect(key).not.toContain("passenger_count");
    });
  });
});

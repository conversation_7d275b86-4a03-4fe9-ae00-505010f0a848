import { MedusaError } from "@camped-ai/framework/utils";

// Create a mock service class for testing the validation methods
class MockSupplierProductsServicesService {
  // Mock the listSupplierOfferingsWithFilters method
  listSupplierOfferingsWithFilters = jest.fn();

  // Copy the validation methods from the actual service
  private doDateRangesOverlap(
    newStart: Date | null,
    newEnd: Date | null,
    existingStart: Date | null,
    existingEnd: Date | null
  ): boolean {
    // Two ranges overlap if: newStart < existingEnd AND newEnd > existingStart
    // Handle null dates as open-ended (extends indefinitely)

    // If both ranges are completely open-ended, they overlap
    if (!newStart && !newEnd && !existingStart && !existingEnd) {
      return true;
    }

    // Check for non-overlapping conditions first (easier to reason about)
    // Case 1: New range ends before existing range starts
    if (newEnd && existingStart && newEnd < existingStart) {
      return false;
    }

    // Case 2: New range starts after existing range ends
    if (newStart && existingEnd && newStart > existingEnd) {
      return false;
    }

    // If we reach here, the ranges overlap
    return true;
  }

  private areDatesIdentical(date1?: Date, date2?: Date): boolean {
    if (!date1 && !date2) return true;
    if (!date1 || !date2) return false;
    return new Date(date1).getTime() === new Date(date2).getTime();
  }

  // Mock the full validation method to test business rules
  async validateSupplierOfferingUniqueness(
    supplierId: string,
    productServiceId: string,
    activeFrom?: Date,
    activeTo?: Date,
    customFields: Record<string, any> = {},
    dynamicFieldSchema: any[] = [],
    excludeId?: string
  ): Promise<void> {
    const existingOfferings = await this.listSupplierOfferingsWithFilters({
      product_service_id: productServiceId,
      supplier_id: supplierId,
    });

    if (existingOfferings?.data?.length > 0) {
      // Rule 3: For updates, check if dates are identical to current record
      if (excludeId) {
        const currentOffering = existingOfferings.data.find((o: any) => o.id === excludeId);
        if (currentOffering) {
          const currentActiveFrom = currentOffering.active_from ? new Date(currentOffering.active_from) : undefined;
          const currentActiveTo = currentOffering.active_to ? new Date(currentOffering.active_to) : undefined;
          const newActiveFrom = activeFrom ? new Date(activeFrom) : undefined;
          const newActiveTo = activeTo ? new Date(activeTo) : undefined;

          const fromIdentical = this.areDatesIdentical(newActiveFrom, currentActiveFrom);
          const toIdentical = this.areDatesIdentical(newActiveTo, currentActiveTo);

          if (fromIdentical && toIdentical) {
            throw new MedusaError(
              MedusaError.Types.DUPLICATE_ERROR,
              "No changes detected: The offering already has the same validity period"
            );
          }
        }
      }

      // Check each existing offering for conflicts
      for (const existingOffering of existingOfferings.data) {
        // Skip the current offering if we're updating
        if (excludeId && existingOffering.id === excludeId) {
          continue;
        }

        // Rule 1: Create Operation - Block Open-Ended Active Offerings
        if (!excludeId) { // Only for create operations
          if (existingOffering.status === "active" && existingOffering.active_to === null) {
            throw new MedusaError(
              MedusaError.Types.DUPLICATE_ERROR,
              "Cannot create offering: An active offering with open-ended validity already exists"
            );
          }
        }

        // Rule 2: Create/Update Operations - Block Date Range Overlaps
        const newStart = activeFrom ? new Date(activeFrom) : null;
        const newEnd = activeTo ? new Date(activeTo) : null;
        const existingStart = existingOffering.active_from ? new Date(existingOffering.active_from) : null;
        const existingEnd = existingOffering.active_to ? new Date(existingOffering.active_to) : null;

        const hasOverlap = this.doDateRangesOverlap(newStart, newEnd, existingStart, existingEnd);

        if (hasOverlap) {
          const formatDate = (date?: Date | null) =>
            date ? date.toISOString().split('T')[0] : 'open-ended';

          throw new MedusaError(
            MedusaError.Types.DUPLICATE_ERROR,
            `Date range conflict: Validity period overlaps with existing offering from ${formatDate(existingStart)} to ${formatDate(existingEnd)}`
          );
        }
      }
    }
  }

  // Expose methods for testing
  public testDoDateRangesOverlap = this.doDateRangesOverlap;
  public testAreDatesIdentical = this.areDatesIdentical;
  public testValidateSupplierOfferingUniqueness = this.validateSupplierOfferingUniqueness;
}

describe("SupplierProductsServicesService - Supplier Offering Validation", () => {
  let service: MockSupplierProductsServicesService;

  beforeEach(() => {
    service = new MockSupplierProductsServicesService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });



  describe("Rule 1: Create Operation - Block Open-Ended Active Offerings", () => {
    it("should block creation when active offering with open-ended validity exists", async () => {
      const existingOffering = {
        id: "existing_1",
        supplier_id: "supplier_123",
        product_service_id: "product_456",
        status: "active",
        active_from: new Date("2024-01-01"),
        active_to: null, // Open-ended
      };

      service.listSupplierOfferingsWithFilters.mockResolvedValue({
        data: [existingOffering],
      });

      await expect(
        service.testValidateSupplierOfferingUniqueness(
          "supplier_123",
          "product_456",
          new Date("2024-06-01"),
          new Date("2024-12-31")
          // No excludeId - this is a create operation
        )
      ).rejects.toThrow("Cannot create offering: An active offering with open-ended validity already exists");
    });

    it("should allow creation when existing offering is inactive (but still check date overlaps)", async () => {
      const existingOffering = {
        id: "existing_1",
        supplier_id: "supplier_123",
        product_service_id: "product_456",
        status: "inactive", // Inactive - Rule 1 doesn't apply
        active_from: new Date("2024-01-01"),
        active_to: new Date("2024-05-31"), // Non-overlapping date range
      };

      service.listSupplierOfferingsWithFilters.mockResolvedValue({
        data: [existingOffering],
      });

      // Should not throw - inactive offering doesn't trigger Rule 1, and dates don't overlap
      await expect(
        service.testValidateSupplierOfferingUniqueness(
          "supplier_123",
          "product_456",
          new Date("2024-06-01"),
          new Date("2024-12-31")
        )
      ).resolves.not.toThrow();
    });

    it("should allow creation when existing offering has end date", async () => {
      const existingOffering = {
        id: "existing_1",
        supplier_id: "supplier_123",
        product_service_id: "product_456",
        status: "active",
        active_from: new Date("2024-01-01"),
        active_to: new Date("2024-05-31"), // Has end date
      };

      service.listSupplierOfferingsWithFilters.mockResolvedValue({
        data: [existingOffering],
      });

      // Should not throw
      await expect(
        service.testValidateSupplierOfferingUniqueness(
          "supplier_123",
          "product_456",
          new Date("2024-06-01"),
          new Date("2024-12-31")
        )
      ).resolves.not.toThrow();
    });
  });

  describe("Rule 2: Create/Update Operations - Block Date Range Overlaps", () => {
    it("should block overlapping date ranges", async () => {
      const existingOffering = {
        id: "existing_1",
        supplier_id: "supplier_123",
        product_service_id: "product_456",
        status: "active",
        active_from: new Date("2024-06-01"),
        active_to: new Date("2024-08-31"),
      };

      service.listSupplierOfferingsWithFilters.mockResolvedValue({
        data: [existingOffering],
      });

      await expect(
        service.testValidateSupplierOfferingUniqueness(
          "supplier_123",
          "product_456",
          new Date("2024-07-01"), // Overlaps with existing range
          new Date("2024-09-30")
        )
      ).rejects.toThrow("Date range conflict: Validity period overlaps with existing offering from 2024-06-01 to 2024-08-31");
    });

    it("should allow non-overlapping date ranges", async () => {
      const existingOffering = {
        id: "existing_1",
        supplier_id: "supplier_123",
        product_service_id: "product_456",
        status: "active",
        active_from: new Date("2024-01-01"),
        active_to: new Date("2024-03-31"),
      };

      service.listSupplierOfferingsWithFilters.mockResolvedValue({
        data: [existingOffering],
      });

      // Should not throw - ranges don't overlap
      await expect(
        service.testValidateSupplierOfferingUniqueness(
          "supplier_123",
          "product_456",
          new Date("2024-06-01"),
          new Date("2024-08-31")
        )
      ).resolves.not.toThrow();
    });

    it("should handle open-ended ranges in overlap detection (for updates)", async () => {
      const existingOffering = {
        id: "existing_1",
        supplier_id: "supplier_123",
        product_service_id: "product_456",
        status: "active",
        active_from: new Date("2024-06-01"),
        active_to: null, // Open-ended
      };

      service.listSupplierOfferingsWithFilters.mockResolvedValue({
        data: [existingOffering],
      });

      // For update operations, Rule 1 doesn't apply, so we should get Rule 2 error
      await expect(
        service.testValidateSupplierOfferingUniqueness(
          "supplier_123",
          "product_456",
          new Date("2024-07-01"), // Overlaps with open-ended range
          new Date("2024-09-30"),
          {},
          [],
          "some_other_offering_id" // This is an update operation
        )
      ).rejects.toThrow("Date range conflict: Validity period overlaps with existing offering from 2024-06-01 to open-ended");
    });
  });

  describe("Rule 3: Update Operation - Block Identical Date Ranges", () => {
    it("should block updating to identical date ranges", async () => {
      const existingOffering = {
        id: "offering_123",
        supplier_id: "supplier_123",
        product_service_id: "product_456",
        status: "active",
        active_from: new Date("2024-01-01"),
        active_to: new Date("2024-12-31"),
      };

      service.listSupplierOfferingsWithFilters.mockResolvedValue({
        data: [existingOffering],
      });

      await expect(
        service.testValidateSupplierOfferingUniqueness(
          "supplier_123",
          "product_456",
          new Date("2024-01-01"), // Same dates
          new Date("2024-12-31"),
          {},
          [],
          "offering_123" // Updating this offering
        )
      ).rejects.toThrow("No changes detected: The offering already has the same validity period");
    });

    it("should allow updating to different date ranges", async () => {
      const existingOffering = {
        id: "offering_123",
        supplier_id: "supplier_123",
        product_service_id: "product_456",
        status: "active",
        active_from: new Date("2024-01-01"),
        active_to: new Date("2024-12-31"),
      };

      service.listSupplierOfferingsWithFilters.mockResolvedValue({
        data: [existingOffering],
      });

      // Should not throw - different dates
      await expect(
        service.testValidateSupplierOfferingUniqueness(
          "supplier_123",
          "product_456",
          new Date("2024-02-01"), // Different start date
          new Date("2024-12-31"),
          {},
          [],
          "offering_123"
        )
      ).resolves.not.toThrow();
    });
  });

  describe("Helper method validation", () => {
    it("should correctly identify identical dates", () => {
      const date1 = new Date("2024-01-01");
      const date2 = new Date("2024-01-01");
      const date3 = new Date("2024-01-02");

      expect(service.testAreDatesIdentical(date1, date2)).toBe(true);
      expect(service.testAreDatesIdentical(date1, date3)).toBe(false);
      expect(service.testAreDatesIdentical(undefined, undefined)).toBe(true);
      expect(service.testAreDatesIdentical(date1, undefined)).toBe(false);
    });
  });

  describe("Date range overlap detection", () => {
    it("should detect overlap when new range starts before existing ends", () => {
      const result = service.testDoDateRangesOverlap(
        new Date("2024-06-01"), // New start
        new Date("2024-08-31"), // New end
        new Date("2024-07-01"), // Existing start
        new Date("2024-09-30")  // Existing end
      );
      expect(result).toBe(true);
    });

    it("should detect overlap when new range ends after existing starts", () => {
      const result = service.testDoDateRangesOverlap(
        new Date("2024-07-01"), // New start
        new Date("2024-09-30"), // New end
        new Date("2024-06-01"), // Existing start
        new Date("2024-08-31")  // Existing end
      );
      expect(result).toBe(true);
    });

    it("should not detect overlap when ranges are separate", () => {
      const result = service.testDoDateRangesOverlap(
        new Date("2024-01-01"), // New start
        new Date("2024-03-31"), // New end
        new Date("2024-06-01"), // Existing start
        new Date("2024-08-31")  // Existing end
      );
      expect(result).toBe(false);
    });

    it("should handle open-ended ranges (null end dates)", () => {
      const result = service.testDoDateRangesOverlap(
        new Date("2024-06-01"), // New start
        null,                   // New end (open)
        new Date("2024-07-01"), // Existing start
        new Date("2024-08-31")  // Existing end
      );
      expect(result).toBe(true);
    });

    it("should handle both ranges being open-ended", () => {
      const result = service.testDoDateRangesOverlap(
        new Date("2024-06-01"), // New start
        null,                   // New end (open)
        new Date("2024-07-01"), // Existing start
        null                    // Existing end (open)
      );
      expect(result).toBe(true);
    });
  });


});

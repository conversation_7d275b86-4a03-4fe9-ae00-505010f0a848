import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { createProductServiceSyncService } from "../utils/service-registration";

/**
 * Subscriber for product service created events
 * Automatically syncs new product services to add-ons
 */
export async function productServiceCreatedHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string; product_service: any }>) {
  try {
    console.log(`🔄 Product service created event received for ID: ${data.id}`);

    // Get the sync service with proper service registration
    const syncService = createProductServiceSyncService(container);

    // Check if auto-sync is enabled for this product service's suppliers
    const productService = data.product_service || { id: data.id };
    
    // If the product service has suppliers, check their auto-sync settings
    if (productService.suppliers && productService.suppliers.length > 0) {
      for (const supplier of productService.suppliers) {
        const config = await syncService.getSupplierConfig(supplier.supplier_id);
        
        if (config.auto_sync_enabled) {
          console.log(`🔄 Auto-sync enabled for supplier ${supplier.supplier_id}, syncing product service ${data.id}`);
          await syncService.syncProductServiceToAddOn(data.id);
          break; // Only sync once even if multiple suppliers have auto-sync enabled
        }
      }
    } else {
      // If no suppliers, use default auto-sync behavior
      console.log(`🔄 No suppliers found, using default sync for product service ${data.id}`);
      await syncService.syncProductServiceToAddOn(data.id);
    }

    console.log(`✅ Product service ${data.id} sync completed successfully`);

  } catch (error) {
    console.error(`❌ Error syncing product service ${data.id}:`, error);
    // Don't throw error to prevent breaking the event flow
    // The error is already logged in the sync service
  }
}

// Default export for the main handler
export default productServiceCreatedHandler;

export const config: SubscriberConfig = {
  event: "product-service.created",
};

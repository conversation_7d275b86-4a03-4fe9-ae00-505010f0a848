import { Migration } from "@camped-ai/framework/utils";

export const migration: Migration = {
  name: "1735600000000-add-cost-currency-to-supplier-offerings",
  up: async (sql) => {
    // Add cost, currency, and currency_override columns to supplier_offering table
    await sql`
      ALTER TABLE supplier_offering 
      ADD COLUMN IF NOT EXISTS cost DECIMAL(10,2),
      ADD COLUMN IF NOT EXISTS currency VARCHAR(3),
      ADD COLUMN IF NOT EXISTS currency_override BOOLEAN DEFAULT FALSE
    `;

    // Add index for cost queries
    await sql`
      CREATE INDEX IF NOT EXISTS IDX_supplier_offering_cost 
      ON supplier_offering(cost) 
      WHERE deleted_at IS NULL
    `;

    // Add index for currency queries
    await sql`
      CREATE INDEX IF NOT EXISTS IDX_supplier_offering_currency 
      ON supplier_offering(currency) 
      WHERE deleted_at IS NULL
    `;
  },
  down: async (sql) => {
    // Remove indexes
    await sql`DROP INDEX IF EXISTS IDX_supplier_offering_cost`;
    await sql`DROP INDEX IF EXISTS IDX_supplier_offering_currency`;

    // Remove columns
    await sql`
      ALTER TABLE supplier_offering 
      DROP COLUMN IF EXISTS cost,
      DROP COLUMN IF EXISTS currency,
      DROP COLUMN IF EXISTS currency_override
    `;
  },
};

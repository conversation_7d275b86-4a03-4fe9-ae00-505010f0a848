import { MedusaContainer } from "@camped-ai/framework/types";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "../modules/supplier-products-services";
import SupplierProductsServicesModuleService from "../modules/supplier-products-services/service";

export default async function testCategoryEdit(container: MedusaContainer): Promise<void> {
  const logger = container.resolve(ContainerRegistrationKeys.LOGGER);

  try {
    logger.info("🧪 Testing category edit functionality...");

    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      container.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    // First, create a test category
    logger.info("📝 Creating test category...");
    const testCategory = await supplierProductsServicesService.createCategory({
      name: "Test Edit Category",
      description: "This is a test category for edit functionality",
      category_type: "Both",
      icon: "🧪",
      is_active: true,
      dynamic_field_schema: [
        {
          label: "Test Field",
          key: "test_field",
          type: "text",
          required: true,
          used_in_filtering: false
        }
      ]
    });

    logger.info("✅ Test category created:", testCategory.id);

    // Now test retrieving the category (simulating the edit page fetch)
    logger.info("📖 Retrieving category for edit...");
    const retrievedCategory = await supplierProductsServicesService.retrieveCategory(testCategory.id);

    logger.info("✅ Category retrieved successfully:");
    logger.info("  - ID:", retrievedCategory.id);
    logger.info("  - Name:", retrievedCategory.name);
    logger.info("  - Description:", retrievedCategory.description);
    logger.info("  - Category Type:", retrievedCategory.category_type);
    logger.info("  - Icon:", retrievedCategory.icon);
    logger.info("  - Is Active:", retrievedCategory.is_active);
    logger.info("  - Dynamic Fields:", JSON.stringify(retrievedCategory.dynamic_field_schema, null, 2));

    // Test updating the category
    logger.info("✏️ Testing category update...");
    const updatedCategory = await supplierProductsServicesService.updateCategory(testCategory.id, {
      name: "Updated Test Category",
      description: "This category has been updated",
      category_type: "Service",
      icon: "✏️",
      is_active: false,
      dynamic_field_schema: [
        {
          label: "Updated Field",
          key: "updated_field",
          type: "number",
          required: false,
          used_in_filtering: true
        },
        {
          label: "New Field",
          key: "new_field",
          type: "dropdown",
          options: ["Option 1", "Option 2"],
          required: true,
          used_in_filtering: false
        }
      ]
    });

    logger.info("✅ Category updated successfully:");
    logger.info("  - ID:", updatedCategory.id);
    logger.info("  - Name:", updatedCategory.name);
    logger.info("  - Description:", updatedCategory.description);
    logger.info("  - Category Type:", updatedCategory.category_type);
    logger.info("  - Icon:", updatedCategory.icon);
    logger.info("  - Is Active:", updatedCategory.is_active);
    logger.info("  - Dynamic Fields:", JSON.stringify(updatedCategory.dynamic_field_schema, null, 2));

    // Verify the changes were persisted
    logger.info("🔍 Verifying changes were persisted...");
    const verifyCategory = await supplierProductsServicesService.retrieveCategory(testCategory.id);

    if (verifyCategory.name === "Updated Test Category" &&
        verifyCategory.category_type === "Service" &&
        verifyCategory.is_active === false &&
        verifyCategory.dynamic_field_schema?.length === 2) {
      logger.info("✅ All changes verified successfully!");
    } else {
      logger.info("❌ Some changes were not persisted correctly");
    }

    // Clean up - delete the test category
    logger.info("🧹 Cleaning up test category...");
    await supplierProductsServicesService.deleteCategory(testCategory.id);
    logger.info("✅ Test category deleted");

    logger.info("🎉 Category edit functionality test completed successfully!");

  } catch (error) {
    logger.error("❌ Category edit test failed:", error);
    logger.error("Error message:", error.message);
    logger.error("Error stack:", error.stack);
  }
}

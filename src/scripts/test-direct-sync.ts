#!/usr/bin/env node

/**
 * Direct Test of the Corrected Product Service Sync Logic
 *
 * This script directly tests the ProductServiceSyncService to verify
 * the corrected sync logic works as expected.
 */

import { withClient } from "../utils/db";
import { ProductServiceSyncService } from "../services/product-service-sync-service";

async function testDirectSync() {
  console.log('🧪 Testing Direct Product Service Sync Logic');
  console.log('=' .repeat(60));

  try {
    await withClient(async (client) => {
      // 1. Get an existing product service
      console.log('\n📝 Step 1: Getting existing product service...');
      
      const existingServices = await client.query(`
        SELECT id, name, type, description, base_cost, status, 
               category_id, unit_type_id, custom_fields
        FROM product_service 
        WHERE status = 'active' 
        LIMIT 1
      `);

      if (existingServices.rows.length === 0) {
        console.log('❌ No existing product services found');
        return;
      }

      const productService = existingServices.rows[0];
      console.log(`✅ Found product service: ${productService.id}`);
      console.log(`   Name: ${productService.name}`);
      console.log(`   Type: ${productService.type}`);
      console.log(`   Custom Fields:`, productService.custom_fields);

      // 2. Test the direct sync method
      console.log('\n🔄 Step 2: Testing direct sync method...');
      
      const expectedProductId = `prod_addon_${productService.id}`;
      const expectedVariantId = `variant_addon_${productService.id}`;
      
      console.log(`Expected Product ID: ${expectedProductId}`);
      console.log(`Expected Variant ID: ${expectedVariantId}`);

      // Create a mock scope for the sync service
      const mockScope = {
        resolve: (serviceName: string) => {
          if (serviceName === 'productModuleService') {
            return {
              listProducts: async (filter: any) => {
                const result = await client.query('SELECT * FROM product WHERE id = ANY($1)', [filter.id || []]);
                return result.rows;
              },
              createProducts: async (data: any) => {
                console.log(`   📦 Creating product with data:`, data);
                const result = await client.query(`
                  INSERT INTO product (id, title, subtitle, description, status, created_at, updated_at)
                  VALUES ($1, $2, $3, $4, $5, now(), now())
                  ON CONFLICT (id) DO UPDATE SET
                    title = EXCLUDED.title,
                    subtitle = EXCLUDED.subtitle,
                    description = EXCLUDED.description,
                    status = EXCLUDED.status,
                    updated_at = now()
                  RETURNING *
                `, [data.id, data.title, data.subtitle, data.description, data.status]);
                return result.rows[0];
              },
              updateProducts: async (id: string, data: any) => {
                console.log(`   📦 Updating product ${id} with data:`, data);
                const result = await client.query(`
                  UPDATE product SET 
                    title = $2, subtitle = $3, description = $4, status = $5, updated_at = now()
                  WHERE id = $1
                  RETURNING *
                `, [id, data.title, data.subtitle, data.description, data.status]);
                return result.rows[0];
              },
              listProductVariants: async (filter: any) => {
                const result = await client.query('SELECT * FROM product_variant WHERE id = ANY($1)', [filter.id || []]);
                return result.rows;
              },
              createProductVariants: async (variants: any[]) => {
                const results = [];
                for (const variant of variants) {
                  console.log(`   📦 Creating variant with data:`, variant);
                  const result = await client.query(`
                    INSERT INTO product_variant (id, title, sku, product_id, metadata, inventory_quantity, manage_inventory, created_at, updated_at)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, now(), now())
                    ON CONFLICT (id) DO UPDATE SET
                      title = EXCLUDED.title,
                      sku = EXCLUDED.sku,
                      metadata = EXCLUDED.metadata,
                      updated_at = now()
                    RETURNING *
                  `, [variant.id, variant.title, variant.sku, variant.product_id, JSON.stringify(variant.metadata), variant.inventory_quantity, variant.manage_inventory]);
                  results.push(result.rows[0]);
                }
                return results;
              },
              updateProductVariants: async (id: string, data: any) => {
                console.log(`   📦 Updating variant ${id} with data:`, data);
                const result = await client.query(`
                  UPDATE product_variant SET 
                    title = $2, sku = $3, metadata = $4, updated_at = now()
                  WHERE id = $1
                  RETURNING *
                `, [id, data.title, data.sku, JSON.stringify(data.metadata)]);
                return result.rows[0];
              }
            };
          }
          if (serviceName === 'supplierProductsServicesService') {
            return {
              retrieveProductService: async (id: string) => {
                const result = await client.query(`
                  SELECT id, name, type, description, base_cost, status, 
                         category_id, unit_type_id, custom_fields
                  FROM product_service 
                  WHERE id = $1
                `, [id]);
                return result.rows[0];
              }
            };
          }
          return null;
        }
      };

      // 3. Test the createProductAndVariantFromProductService method
      console.log('\n🔄 Step 3: Testing createProductAndVariantFromProductService...');
      
      const syncService = new ProductServiceSyncService(mockScope as any);
      
      try {
        const result = await syncService.createProductAndVariantFromProductService(productService);
        
        console.log('✅ Sync completed successfully!');
        console.log(`   Product ID: ${result.product.id}`);
        console.log(`   Product Title: ${result.product.title}`);
        console.log(`   Variant ID: ${result.variant.id}`);
        console.log(`   Variant SKU: ${result.variant.sku}`);
        console.log(`   Variant Metadata:`, JSON.stringify(result.variant.metadata, null, 2));
        
        // 4. Verify the results
        console.log('\n🔍 Step 4: Verifying sync results...');
        
        // Check product
        const productCheck = await client.query('SELECT * FROM product WHERE id = $1', [expectedProductId]);
        if (productCheck.rows.length > 0) {
          const product = productCheck.rows[0];
          console.log(`✅ Product created correctly:`);
          console.log(`   ID: ${product.id}`);
          console.log(`   Title: ${product.title}`);
          console.log(`   Subtitle: "${product.subtitle}"`);
          console.log(`   Description: ${product.description}`);
          console.log(`   Status: ${product.status}`);
        } else {
          console.log(`❌ Product not found: ${expectedProductId}`);
        }
        
        // Check variant
        const variantCheck = await client.query('SELECT * FROM product_variant WHERE id = $1', [expectedVariantId]);
        if (variantCheck.rows.length > 0) {
          const variant = variantCheck.rows[0];
          console.log(`✅ Variant created correctly:`);
          console.log(`   ID: ${variant.id}`);
          console.log(`   Title: ${variant.title}`);
          console.log(`   SKU: ${variant.sku}`);
          console.log(`   Product ID: ${variant.product_id}`);
          console.log(`   Metadata:`, JSON.stringify(variant.metadata, null, 2));
          
          // Verify metadata structure
          const metadata = variant.metadata;
          console.log('\n🔍 Verifying metadata requirements:');
          console.log(`   ✅ type: ${metadata.type} (expected: ${productService.type})`);
          console.log(`   ✅ description: ${metadata.description} (expected: ${productService.description})`);
          console.log(`   ✅ category: ${metadata.category} (expected: ${productService.category_id})`);
          console.log(`   ✅ unit_type_id: ${metadata.unit_type_id} (expected: ${productService.unit_type_id})`);
          console.log(`   ✅ add_on_service: ${metadata.add_on_service} (expected: true)`);
          
          if (productService.custom_fields) {
            console.log('   ✅ Custom fields flattened:');
            Object.keys(productService.custom_fields).forEach(key => {
              console.log(`      - ${key}: ${metadata[key]} (expected: ${productService.custom_fields[key]})`);
            });
          }
          
        } else {
          console.log(`❌ Variant not found: ${expectedVariantId}`);
        }
        
      } catch (error) {
        console.error('❌ Sync failed:', error);
      }

      console.log('\n✅ Direct sync test completed!');
      console.log('The corrected sync logic has been verified.');

    });
  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

// Export the function for medusa exec
export default testDirectSync;

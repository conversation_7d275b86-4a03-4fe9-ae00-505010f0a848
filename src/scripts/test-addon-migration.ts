#!/usr/bin/env node

/**
 * Simple test script for add-on migration
 * This script tests the basic functionality without command line arguments
 */

import { <PERSON>dusaContainer } from "@camped-ai/framework/types";
import { withClient } from "../utils/db";

async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...');

  try {
    await withClient(async (client) => {
      const result = await client.query('SELECT 1 as test');
      console.log('✅ Database connection successful:', result.rows[0]);
    });
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    throw error;
  }
}

async function checkExistingTables() {
  console.log('🔍 Checking existing tables...');

  try {
    return await withClient(async (client) => {
      // Check if supplier tables exist
      const tableCheckQuery = `
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name IN ('supplier', 'product_service', 'product_service_supplier', 'add_on_supplier_config', 'add_on_sync_log', 'add_on_migration_backup')
      `;

      const result = await client.query(tableCheckQuery);
      const existingTables = result.rows.map(row => row.table_name);

      const supplierExists = existingTables.includes('supplier');
      const productServiceExists = existingTables.includes('product_service');
      const productServiceSupplierExists = existingTables.includes('product_service_supplier');
      const configExists = existingTables.includes('add_on_supplier_config');
      const syncLogExists = existingTables.includes('add_on_sync_log');
      const backupExists = existingTables.includes('add_on_migration_backup');

      console.log('📊 Table Status:');
      console.log(`  - supplier: ${supplierExists ? '✅' : '❌'}`);
      console.log(`  - product_service: ${productServiceExists ? '✅' : '❌'}`);
      console.log(`  - product_service_supplier: ${productServiceSupplierExists ? '✅' : '❌'}`);

      console.log('📊 Migration Table Status:');
      console.log(`  - add_on_supplier_config: ${configExists ? '✅' : '❌'}`);
      console.log(`  - add_on_sync_log: ${syncLogExists ? '✅' : '❌'}`);
      console.log(`  - add_on_migration_backup: ${backupExists ? '✅' : '❌'}`);

      return {
        supplier: supplierExists,
        productService: productServiceExists,
        productServiceSupplier: productServiceSupplierExists,
        config: configExists,
        syncLog: syncLogExists,
        backup: backupExists
      };
    });
  } catch (error) {
    console.error('❌ Table check failed:', error.message);
    throw error;
  }
}

async function checkSupplierData() {
  console.log('🔍 Checking supplier data...');

  try {
    return await withClient(async (client) => {
      // Check supplier count
      const supplierResult = await client.query(`
        SELECT COUNT(*) as count
        FROM supplier
        WHERE deleted_at IS NULL
      `);

      const supplierCount = parseInt(supplierResult.rows[0]?.count || '0');
      console.log(`📊 Active suppliers: ${supplierCount}`);

      // Check product service count
      const productServiceResult = await client.query(`
        SELECT COUNT(*) as count
        FROM product_service
        WHERE status = 'active' AND deleted_at IS NULL
      `);

      const productServiceCount = parseInt(productServiceResult.rows[0]?.count || '0');
      console.log(`📊 Active product/services: ${productServiceCount}`);

      // Check product service supplier relationships
      const relationshipResult = await client.query(`
        SELECT COUNT(*) as count
        FROM product_service_supplier
        WHERE is_active = true AND deleted_at IS NULL
      `);

      const relationshipCount = parseInt(relationshipResult.rows[0]?.count || '0');
      console.log(`📊 Active supplier-product relationships: ${relationshipCount}`);

      return {
        suppliers: supplierCount,
        productServices: productServiceCount,
        relationships: relationshipCount
      };
    });
  } catch (error) {
    console.error('❌ Supplier data check failed:', error.message);
    throw error;
  }
}

async function checkAddOnData() {
  console.log('🔍 Checking existing add-on data...');

  try {
    return await withClient(async (client) => {
      // Check if product tables exist
      const tableCheckQuery = `
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name IN ('product', 'product_variant')
      `;

      const result = await client.query(tableCheckQuery);
      const existingTables = result.rows.map(row => row.table_name);

      const productExists = existingTables.includes('product');
      const productVariantExists = existingTables.includes('product_variant');

      console.log('📊 Add-on Related Tables:');
      console.log(`  - product: ${productExists ? '✅' : '❌'}`);
      console.log(`  - product_variant: ${productVariantExists ? '✅' : '❌'}`);

      if (productExists) {
        const productCountResult = await client.query(`
          SELECT COUNT(*) as count
          FROM product
          WHERE deleted_at IS NULL
        `);

        console.log(`📊 Total products: ${parseInt(productCountResult.rows[0]?.count || '0')}`);
      }

      return {
        productTable: productExists,
        productVariantTable: productVariantExists
      };
    });
  } catch (error) {
    console.error('❌ Add-on data check failed:', error.message);
    throw error;
  }
}

async function runMigrationTest() {
  console.log('🚀 Starting Add-on Migration Test');
  console.log('=' .repeat(50));

  try {
    // Test 1: Database connection
    await testDatabaseConnection();

    // Test 2: Check existing tables
    const tableStatus = await checkExistingTables();

    // Test 3: Check supplier data
    const supplierData = await checkSupplierData();

    // Test 4: Check add-on data
    const addOnData = await checkAddOnData();

    console.log('\n📊 MIGRATION READINESS SUMMARY');
    console.log('=' .repeat(50));

    // Determine readiness
    const isReady = tableStatus.supplier &&
                   tableStatus.productService &&
                   tableStatus.productServiceSupplier &&
                   supplierData.suppliers > 0 &&
                   supplierData.productServices > 0;

    console.log(`Status: ${isReady ? '✅ READY' : '❌ NOT READY'}`);
    console.log(`Suppliers: ${supplierData.suppliers}`);
    console.log(`Product/Services: ${supplierData.productServices}`);
    console.log(`Relationships: ${supplierData.relationships}`);

    if (!isReady) {
      console.log('\n⚠️  PREREQUISITES MISSING:');
      if (!tableStatus.supplier) console.log('  - Supplier table missing');
      if (!tableStatus.productService) console.log('  - Product service table missing');
      if (!tableStatus.productServiceSupplier) console.log('  - Product service supplier table missing');
      if (supplierData.suppliers === 0) console.log('  - No active suppliers found');
      if (supplierData.productServices === 0) console.log('  - No active product/services found');
    }

    // Check if migration tables are ready
    const migrationReady = tableStatus.config && tableStatus.syncLog && tableStatus.backup;
    console.log(`Migration Tables: ${migrationReady ? '✅ READY' : '❌ NOT READY'}`);

    if (!migrationReady) {
      console.log('\n💡 NEXT STEPS:');
      console.log('  1. Run database migration: npm run migration:run');
      console.log('  2. Ensure all required tables are created');
      console.log('  3. Re-run this test');
    } else {
      console.log('\n🎯 READY FOR MIGRATION:');
      console.log('  1. Run dry-run migration test');
      console.log('  2. Execute full migration');
      console.log('  3. Validate results');
    }

    console.log('=' .repeat(50));

  } catch (error) {
    console.error('\n❌ Migration test failed:', error);
    process.exit(1);
  }
}

// Export the function for medusa exec
export default runMigrationTest;
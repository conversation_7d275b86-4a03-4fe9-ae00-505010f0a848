import { withClient } from "../utils/db";
import { randomUUID } from "crypto";

/**
 * <PERSON><PERSON><PERSON> to test the product_service_supplier functionality
 */
async function testProductServiceSupplier() {
  try {
    console.log("🚀 Testing product_service_supplier functionality...");

    // First, check if the table exists and has any records
    await withClient(async (client) => {
      console.log("🔍 Checking if product_service_supplier table exists...");
      const tableCheck = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'product_service_supplier'
        );
      `);
      
      console.log(`✅ Table exists: ${tableCheck.rows[0].exists}`);

      if (tableCheck.rows[0].exists) {
        // Count existing records
        const countResult = await client.query(`SELECT COUNT(*) FROM "product_service_supplier";`);
        console.log(`✅ Current record count: ${countResult.rows[0].count}`);

        // List all records
        const records = await client.query(`SELECT * FROM "product_service_supplier";`);
        console.log("✅ Existing records:", records.rows);

        // Get a product service to link to
        const productServices = await client.query(`SELECT * FROM "product_service" LIMIT 1;`);
        if (productServices.rows.length > 0) {
          const productService = productServices.rows[0];
          console.log(`✅ Found product service to link: ${productService.id} - ${productService.name}`);

          // Get a supplier to link to
          const suppliers = await client.query(`SELECT * FROM "supplier" LIMIT 1;`);
          if (suppliers.rows.length > 0) {
            const supplier = suppliers.rows[0];
            console.log(`✅ Found supplier to link: ${supplier.id} - ${supplier.name}`);

            // Create a test record
            const id = `pss_${randomUUID().replace(/-/g, '')}`;
            console.log(`🔍 Creating test record with ID: ${id}`);
            
            // Check if this link already exists
            const existingLink = await client.query(`
              SELECT * FROM "product_service_supplier" 
              WHERE product_service_id = $1 AND supplier_id = $2;
            `, [productService.id, supplier.id]);
            
            if (existingLink.rows.length > 0) {
              console.log(`⚠️ Link already exists between product service ${productService.id} and supplier ${supplier.id}`);
              console.log("✅ Existing link:", existingLink.rows[0]);
            } else {
              // Insert a new record
              await client.query(`
                INSERT INTO "product_service_supplier" (
                  "id", "product_service_id", "supplier_id", "cost", "currency_code", 
                  "availability", "is_active", "is_preferred", "created_at", "updated_at"
                ) VALUES (
                  $1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW()
                );
              `, [id, productService.id, supplier.id, 100.00, 'CHF', 'available', true, false]);
              
              console.log(`✅ Created new link between product service ${productService.id} and supplier ${supplier.id}`);
              
              // Verify the record was created
              const newRecord = await client.query(`SELECT * FROM "product_service_supplier" WHERE id = $1;`, [id]);
              console.log("✅ New record created:", newRecord.rows[0]);
            }
          } else {
            console.log("❌ No suppliers found to link");
          }
        } else {
          console.log("❌ No product services found to link");
        }
      }
    });

    console.log("🎉 Test completed successfully!");
  } catch (error) {
    console.error("❌ Test failed:", error);
    throw error;
  }
}

// Run the script if called directly
if (require.main === module) {
  testProductServiceSupplier()
    .then(() => {
      console.log("Script completed successfully");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Script failed:", error);
      process.exit(1);
    });
}

export default testProductServiceSupplier;

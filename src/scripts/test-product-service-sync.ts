#!/usr/bin/env node

/**
 * Test script for Product Service to Add-on Synchronization
 * 
 * This script tests:
 * 1. Event-driven synchronization
 * 2. Bulk synchronization
 * 3. Error handling
 * 4. Data integrity
 * 5. Idempotent operations
 */

import { ProductServiceSyncService } from "../services/product-service-sync-service.js";
import { withClient } from "../utils/db.js";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "../modules/supplier-products-services/index.js";
import { ADD_ON_SERVICE } from "../modules/hotel-management/add-on-service/index.js";
import { Modules } from "@camped-ai/framework/utils";

interface TestResult {
  test: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  message: string;
  duration: number;
}

class ProductServiceSyncTester {
  private results: TestResult[] = [];
  private container: any;
  private syncService: ProductServiceSyncService;
  private supplierProductsServicesService: any;
  private addOnService: any;
  private productModuleService: any;

  constructor(container: any) {
    this.container = container;
    this.syncService = new ProductServiceSyncService(container);
    this.supplierProductsServicesService = container.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);
    this.addOnService = container.resolve(ADD_ON_SERVICE);
    this.productModuleService = container.resolve(Modules.PRODUCT);
  }

  private async runTest(testName: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = Date.now();
    try {
      console.log(`🧪 Running test: ${testName}`);
      await testFn();
      const duration = Date.now() - startTime;
      this.results.push({
        test: testName,
        status: 'PASS',
        message: 'Test passed successfully',
        duration
      });
      console.log(`✅ ${testName} - PASSED (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      this.results.push({
        test: testName,
        status: 'FAIL',
        message: error.message,
        duration
      });
      console.log(`❌ ${testName} - FAILED (${duration}ms): ${error.message}`);
    }
  }

  private async createTestProductService(): Promise<any> {
    // Create test category and unit type first
    const timestamp = Date.now();
    
    const categories = await this.supplierProductsServicesService.createCategories([{
      name: `Test Category ${timestamp}`,
      description: "Test category for sync testing",
      is_active: true,
    }]);

    const unitTypes = await this.supplierProductsServicesService.createUnitTypes([{
      name: `Test Unit ${timestamp}`,
      description: "Test unit type for sync testing",
      is_active: true,
    }]);

    // Create test product service
    const productService = await this.supplierProductsServicesService.createProductService({
      type: "Service",
      description: "Test service for sync testing",
      base_cost: 100.00,
      status: "active",
      category_id: categories[0].id,
      unit_type_id: unitTypes[0].id,
    });

    return productService;
  }

  private async cleanupTestData(productServiceId: string): Promise<void> {
    try {
      // Find and delete associated add-on
      const addOn = await this.syncService.findAddOnByProductServiceId(productServiceId);
      if (addOn) {
        await this.productModuleService.deleteProducts([addOn.id]);
      }

      // Delete product service
      await this.supplierProductsServicesService.deleteProductService(productServiceId);
    } catch (error) {
      console.warn(`Warning: Failed to cleanup test data: ${error.message}`);
    }
  }

  async testSingleProductServiceSync(): Promise<void> {
    let productService: any = null;
    
    try {
      // Create test product service
      productService = await this.createTestProductService();
      
      // Test sync
      const result = await this.syncService.syncProductServiceToAddOn(productService.id);
      
      // Verify add-on was created
      if (!result || !result.id) {
        throw new Error("Add-on was not created");
      }

      // Verify add-on has correct metadata
      const addOn = await this.productModuleService.retrieveProduct(result.product_id, {
        relations: ["variants", "variants.prices"]
      });

      if (!addOn.metadata?.add_on_service) {
        throw new Error("Add-on metadata is missing add_on_service flag");
      }

      // Verify pricing
      if (!addOn.variants || addOn.variants.length === 0) {
        throw new Error("Add-on has no variants");
      }

      console.log(`✅ Created add-on: ${addOn.title} with ${addOn.variants.length} variants`);
      
    } finally {
      if (productService) {
        await this.cleanupTestData(productService.id);
      }
    }
  }

  async testIdempotentSync(): Promise<void> {
    let productService: any = null;
    
    try {
      // Create test product service
      productService = await this.createTestProductService();
      
      // Sync twice
      const result1 = await this.syncService.syncProductServiceToAddOn(productService.id);
      const result2 = await this.syncService.syncProductServiceToAddOn(productService.id);
      
      // Should return the same add-on (updated, not duplicated)
      if (result1.product_id !== result2.product_id) {
        throw new Error("Sync is not idempotent - created duplicate add-ons");
      }

      console.log(`✅ Idempotent sync verified - same add-on ID: ${result1.product_id}`);
      
    } finally {
      if (productService) {
        await this.cleanupTestData(productService.id);
      }
    }
  }

  async testBulkSync(): Promise<void> {
    const productServices: any[] = [];
    
    try {
      // Create multiple test product services
      for (let i = 0; i < 3; i++) {
        const productService = await this.createTestProductService();
        productServices.push(productService);
      }
      
      // Test bulk sync
      const result = await this.syncService.bulkSyncProductServicesToAddOns({
        limit: 10,
        offset: 0,
      });
      
      // Verify results
      if (result.synced < productServices.length) {
        throw new Error(`Bulk sync incomplete: ${result.synced}/${productServices.length} synced`);
      }

      if (result.errors.length > 0) {
        throw new Error(`Bulk sync had errors: ${result.errors.map(e => e.error).join(', ')}`);
      }

      console.log(`✅ Bulk sync completed: ${result.synced} product services synced`);
      
    } finally {
      // Cleanup all test data
      for (const productService of productServices) {
        await this.cleanupTestData(productService.id);
      }
    }
  }

  async testSyncStatistics(): Promise<void> {
    const stats = await this.syncService.getSyncStatistics();
    
    if (typeof stats.total_product_services !== 'number') {
      throw new Error("Invalid statistics: total_product_services is not a number");
    }

    if (typeof stats.synced_count !== 'number') {
      throw new Error("Invalid statistics: synced_count is not a number");
    }

    console.log(`✅ Statistics retrieved: ${stats.synced_count}/${stats.total_product_services} synced`);
  }

  async testErrorHandling(): Promise<void> {
    try {
      // Test sync with non-existent product service
      await this.syncService.syncProductServiceToAddOn("non-existent-id");
      throw new Error("Should have thrown an error for non-existent product service");
    } catch (error) {
      if (!error.message.includes("not found")) {
        throw new Error(`Unexpected error message: ${error.message}`);
      }
      console.log(`✅ Error handling verified: ${error.message}`);
    }
  }

  async testSupplierConfig(): Promise<void> {
    const config = await this.syncService.getSupplierConfig("test-supplier-id");
    
    if (!config || typeof config.default_margin_percentage !== 'number') {
      throw new Error("Invalid supplier config structure");
    }

    if (config.default_currency !== 'CHF') {
      throw new Error("Default currency should be CHF");
    }

    console.log(`✅ Supplier config retrieved: ${config.default_margin_percentage}% margin`);
  }

  async runAllTests(): Promise<void> {
    console.log("🚀 Starting Product Service Sync Tests...\n");

    await this.runTest("Single Product Service Sync", () => this.testSingleProductServiceSync());
    await this.runTest("Idempotent Sync", () => this.testIdempotentSync());
    await this.runTest("Bulk Sync", () => this.testBulkSync());
    await this.runTest("Sync Statistics", () => this.testSyncStatistics());
    await this.runTest("Error Handling", () => this.testErrorHandling());
    await this.runTest("Supplier Config", () => this.testSupplierConfig());

    // Print summary
    console.log("\n📊 Test Results Summary:");
    console.log("========================");
    
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);

    this.results.forEach(result => {
      const icon = result.status === 'PASS' ? '✅' : '❌';
      console.log(`${icon} ${result.test} (${result.duration}ms)`);
      if (result.status === 'FAIL') {
        console.log(`   Error: ${result.message}`);
      }
    });

    console.log(`\nTotal: ${this.results.length} tests`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${failed}`);
    console.log(`Duration: ${totalDuration}ms`);

    if (failed > 0) {
      process.exit(1);
    }
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  (async () => {
    try {
      // This would need to be properly initialized with the actual container
      // For now, this is a template showing how the tests would work
      console.log("⚠️  This is a test template. To run actual tests, initialize with proper container.");
      console.log("Example usage:");
      console.log("const tester = new ProductServiceSyncTester(container);");
      console.log("await tester.runAllTests();");
    } catch (error) {
      console.error("Test execution failed:", error);
      process.exit(1);
    }
  })();
}

export { ProductServiceSyncTester };

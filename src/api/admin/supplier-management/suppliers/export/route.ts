import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { SUPPLIER_MANAGEMENT_MODULE } from "src/modules/vendor_management";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierManagementModuleService from "src/modules/vendor_management/supplier-service";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";
import * as ExcelJS from 'exceljs';
import {
  LANGUAGES,
  REGIONS,
  TIMEZONES,
  PAYMENT_METHODS,
  PAYOUT_TERMS,
  CURRENCIES,
  SUPPLIER_STATUSES,
  SUPPLIER_TYPES
} from "../../../../../admin/constants/supplier-form-options";



// Query validation schema
const GetAdminExportSuppliersQuery = z.object({
  fields: z.string().optional(),
  is_active: z.enum(["all", "true", "false"]).optional().default("all"),
  supplier_type: z.enum(["all", "company", "individual"]).optional().default("all"),
  format: z.enum(["csv", "xlsx", "excel"]).optional().default("xlsx"),
});

type GetAdminExportSuppliersQueryType = z.infer<typeof GetAdminExportSuppliersQuery>;

/**
 * GET /admin/supplier-management/suppliers/export
 * Export suppliers data in CSV or Excel format matching import template
 */
export const GET = async (
  req: MedusaRequest<{}, GetAdminExportSuppliersQueryType>,
  res: MedusaResponse
) => {
  try {
    const supplierManagementService: SupplierManagementModuleService = 
      req.scope.resolve(SUPPLIER_MANAGEMENT_MODULE);

    // Parse and validate query parameters
    const validatedQuery = GetAdminExportSuppliersQuery.parse(req.query);
    const { fields, is_active, supplier_type, format } = validatedQuery;

    // Build filters for suppliers
    const supplierFilters: any = {};

    if (is_active !== "all") {
      // Map the filter values to actual status values (capitalized to match database enum)
      if (is_active === "true") {
        supplierFilters.status = "Active";
      } else if (is_active === "false") {
        supplierFilters.status = "Inactive";
      }
    }

    if (supplier_type !== "all") {
      supplierFilters.supplier_type = supplier_type;
    }



    // Fetch suppliers with filters
    const suppliersResult = await supplierManagementService.listSuppliers(
      supplierFilters,
      { skip: 0, take: 10000 } // Large limit to get all data for export
    );

    const suppliers = suppliersResult.suppliers || [];

    // Fetch all contacts for the suppliers
    const supplierIds = suppliers.map((s: any) => s.id);
    const allContacts: any[] = [];

    for (const supplierId of supplierIds) {
      try {
        const contacts = await supplierManagementService.getSupplierContacts(supplierId);
        const supplierName = suppliers.find((s: any) => s.id === supplierId)?.name || '';

        contacts.forEach((contact: any) => {
          allContacts.push({
            supplier_name: supplierName,
            contact_name: contact.name,
            contact_email: contact.email,
            contact_phone: contact.phone_number || '',
            is_whatsapp: contact.is_whatsapp ? 'true' : 'false',
            is_primary: contact.is_primary ? 'true' : 'false'
          });
        });
      } catch (error) {
        console.error(`Error fetching contacts for supplier ${supplierId}:`, error);
      }
    }

    if (suppliers.length === 0) {
      return res.status(404).json({
        type: "no_data",
        message: "No suppliers found matching the specified criteria",
      });
    }

    // Fetch categories for ID-to-name mapping BEFORE data transformation
    let categoryMap = new Map<string, string>(); // Map category ID to name
    try {
      const supplierProductsServicesService: SupplierProductsServicesModuleService =
        req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

      const categoriesResult = await supplierProductsServicesService.listCategories(
        { is_active: true },
        { skip: 0, take: 1000 }
      );

      // Handle different return formats from generated method
      let categories: any[] = [];
      if (Array.isArray(categoriesResult)) {
        categories = categoriesResult;
      } else if (categoriesResult && typeof categoriesResult === 'object') {
        const result = categoriesResult as any;
        if ('categories' in result && Array.isArray(result.categories)) {
          categories = result.categories;
        } else if ('data' in result && Array.isArray(result.data)) {
          categories = result.data;
        }
      }

      if (categories && categories.length > 0) {
        // Create ID-to-name mapping for export data transformation
        categories.forEach((category: any) => {
          if (category.id && category.name) {
            categoryMap.set(category.id, category.name);
          }
        });
      }
    } catch (error) {
      console.warn('Failed to fetch categories, using fallback values:', error);
    }

    // Define all available fields that match the import template
    const allFields = [
      'name',
      'supplier_type',
      'website',
      'status',
      'preference',
      'timezone',
      'language_preference',
      'payment_method',
      'payout_terms',
      'tax_id',
      'default_currency',
      'bank_account_details',
      'categories',
      'address'
    ];

    // Parse selected fields or use all fields
    const selectedFields = fields ? fields.split(',').filter(f => allFields.includes(f)) : allFields;

    // Transform suppliers data for export (matching import template format exactly)
    const exportData = suppliers
      .filter((supplier: any) => {
        // Filter out suppliers with missing critical data to prevent import errors
        const hasName = supplier.name && supplier.name.toString().trim() !== '';

        if (!hasName) {
          console.warn(`⚠️ Skipping supplier "${supplier.name || 'Unknown'}" - missing required fields`);
          return false;
        }
        return true;
      })
      .map((supplier: any) => {
        const exportRecord: any = {};

        // Required fields for import validation (must have values)
        const requiredFields = ['name'];

      // Process fields in the order they appear in the template headers
      const templateHeaders = [
        "name", "supplier_type", "website", "status",
        "preference", "timezone", "language_preference",
        "payment_method", "payout_terms", "tax_id", "default_currency",
        "bank_account_details", "categories", "address"
      ];

      templateHeaders.forEach(field => {
        // Only include fields that are selected for export
        if (!selectedFields.includes(field)) return;
        switch (field) {
          case 'supplier_type':
            // Ensure supplier_type matches validation schema format (Company/Individual)
            const supplierType = supplier[field] || '';
            // Map database values to validation schema format
            const supplierTypeMap: { [key: string]: string } = {
              'company': 'Company',
              'individual': 'Individual'
            };
            exportRecord[field] = supplierTypeMap[supplierType.toLowerCase()] || 'Company';
            break;
          case 'status':
            // Ensure status matches validation schema format (Active/Inactive/etc.)
            const status = supplier[field] || '';
            // Map database values to validation schema format (handle both lowercase and capitalized)
            const statusMap: { [key: string]: string } = {
              'active': 'Active',
              'inactive': 'Inactive',
              'pending approval': 'Pending Approval',
              'suspended': 'Suspended',
              'terminated': 'Terminated'
            };
            // If status is already capitalized, use it; otherwise map from lowercase
            const normalizedStatus = statusMap[status.toLowerCase()] || status || 'Active';
            exportRecord[field] = normalizedStatus;
            break;
          case 'categories':
            // Convert categories array from IDs to names, then to comma-separated string
            if (Array.isArray(supplier.categories)) {
              const categoryNames = supplier.categories
                .map((categoryId: string) => categoryMap.get(categoryId) || categoryId)
                .filter((name: string) => name)
                .join(',');
              exportRecord[field] = categoryNames;
            } else {
              exportRecord[field] = supplier.categories || '';
            }
            break;
          case 'language_preference':
            // Convert language preference codes to labels for Excel display
            if (Array.isArray(supplier.language_preference)) {
              const languageLabels = supplier.language_preference
                .map((code: any) => {
                  const language = LANGUAGES.find(lang => lang.value === code);
                  return language ? language.label : code; // fallback to code if not found
                })
                .filter((label: any) => label); // remove empty values
              exportRecord[field] = languageLabels.join(',');
            } else if (supplier.language_preference) {
              // Handle single language code
              const language = LANGUAGES.find(lang => lang.value === supplier.language_preference);
              exportRecord[field] = language ? language.label : supplier.language_preference;
            } else {
              exportRecord[field] = '';
            }
            break;

          default:
            let value = supplier[field] || '';

            // For required fields, provide meaningful defaults if empty
            if (requiredFields.includes(field) && (!value || value.toString().trim() === '')) {
              switch (field) {
                case 'name':
                  value = 'Supplier Name'; // This should never be empty, but just in case
                  break;
                default:
                  value = value || '';
              }
            }

            exportRecord[field] = value;
        }
      });

      // Ensure the export record has all template fields in the correct order
      const orderedRecord: any = {};
      templateHeaders.forEach(header => {
        if (selectedFields.includes(header)) {
          orderedRecord[header] = exportRecord[header] || '';
        }
      });

      return orderedRecord;
    });

    const timestamp = new Date().toISOString().split('T')[0];

    if (format === "csv") {
      // Generate CSV
      const csvContent = convertToCSV(exportData);
      const filename = `suppliers_export_${timestamp}.csv`;

      res.setHeader('Content-Type', 'text/csv;charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', Buffer.byteLength(csvContent, 'utf8'));

      return res.send(csvContent);
    } else {
      // Generate Excel using ExcelJS (matching import template exactly)
      await generateExcelExportV2(req, res, exportData, allContacts, timestamp, categoryMap);
    }

  } catch (error) {
    console.error("Export error:", error);
    return res.status(500).json({
      type: "export_error",
      message: error instanceof Error ? error.message : "Failed to export suppliers data",
    });
  }
};



// Helper function to convert data to CSV
function convertToCSV(data: any[]): string {
  if (data.length === 0) return '';

  const headers = Object.keys(data[0]);
  const csvRows = [
    headers.join(','),
    ...data.map(row =>
      headers.map(header => {
        const value = row[header] || '';
        // Escape quotes and wrap in quotes if contains comma or quote
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      }).join(',')
    )
  ];

  return csvRows.join('\n');
}

// Helper function to setup suppliers sheet with formatting and dropdowns
const setupSuppliersSheet = (worksheet: ExcelJS.Worksheet, headers: string[], exportData: any[]) => {
  // Set up headers with proper formatting
  const headerRow = worksheet.addRow(headers);
  headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
  headerRow.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: '4472C4' }
  };

  // Set column widths - added width for preference column
  const columnWidths = [25, 15, 30, 20, 30, 12, 20, 15, 20, 12, 25, 20, 15, 15, 25, 30, 25, 25, 25, 20, 15, 15, 20];
  columnWidths.forEach((width, index) => {
    worksheet.getColumn(index + 1).width = width;
  });

  // Add export data with proper handling of null/undefined values
  exportData.forEach(data => {
    const values = Object.values(data).map(value => {
      // Handle null, undefined, and complex objects
      if (value === null || value === undefined) {
        return '';
      }
      if (typeof value === 'object') {
        return JSON.stringify(value);
      }
      return value;
    });
    const row = worksheet.addRow(values);
    row.font = { color: { argb: '000000' } };
  });

  // Apply dropdown validation using options from supplier-form-options
  const dropdownOptions = {
    supplier_type: SUPPLIER_TYPES.map(option => option.value),
    status: SUPPLIER_STATUSES.map(option => option.value),

    preference: ['Preferred', 'Backup'], // Static preference options
    region: REGIONS.map(option => option.value),
    timezone: TIMEZONES.map(option => option.value),
    payment_method: PAYMENT_METHODS.map(option => option.value),
    payout_terms: PAYOUT_TERMS.map(option => option.value),
    default_currency: CURRENCIES.map(option => option.value) // Currency codes only
  };

  Object.entries(dropdownOptions).forEach(([fieldName, options]) => {
    const columnIndex = headers.indexOf(fieldName) + 1;
    if (columnIndex > 0 && options.length > 0) {
      // Clean and sanitize options to prevent Excel corruption
      const cleanOptions = options
        .filter(option => option && typeof option === 'string')
        .map(option => option.toString().trim())
        .filter(option => option.length > 0)
        .slice(0, 50); // Limit to 50 options to prevent Excel issues

      if (cleanOptions.length === 0) {
        console.warn(`No valid options found for ${fieldName}, skipping dropdown validation`);
        return;
      }

      // Create a safe formula for dropdown validation
      // Excel has a limit of 255 characters for direct list validation
      const optionsString = cleanOptions.join(',');

      // If the options string is too long, we'll use a different approach
      if (optionsString.length > 255) {
        console.warn(`Dropdown options for ${fieldName} are too long (${optionsString.length} chars), using truncated list`);
        // Use only the first few options that fit within the limit
        let truncatedOptions = [];
        let currentLength = 0;
        for (const option of cleanOptions) {
          if (currentLength + option.length + 1 <= 250) { // Leave some buffer
            truncatedOptions.push(option);
            currentLength += option.length + 1; // +1 for comma
          } else {
            break;
          }
        }

        if (truncatedOptions.length === 0) {
          console.warn(`No options fit within Excel limit for ${fieldName}, skipping dropdown validation`);
          return;
        }

        for (let rowIndex = 2; rowIndex <= Math.max(1000, exportData.length + 100); rowIndex++) {
          const cell = worksheet.getCell(rowIndex, columnIndex);
          try {
            cell.dataValidation = {
              type: 'list',
              allowBlank: true,
              formulae: [`"${truncatedOptions.join(',')}"`],
              showErrorMessage: true,
              errorStyle: 'error',
              errorTitle: 'Invalid Value',
              error: `Please select a value from the dropdown list`,
              showInputMessage: true,
              promptTitle: `Select ${fieldName.replace(/_/g, ' ')}`,
              prompt: `Choose from available options`
            };
          } catch (validationError) {
            console.warn(`Failed to set validation for ${fieldName} at row ${rowIndex}:`, validationError);
          }
        }
      } else {
        // Standard approach for shorter option lists
        for (let rowIndex = 2; rowIndex <= Math.max(1000, exportData.length + 100); rowIndex++) {
          const cell = worksheet.getCell(rowIndex, columnIndex);
          try {
            cell.dataValidation = {
              type: 'list',
              allowBlank: true,
              formulae: [`"${cleanOptions.join(',')}"`],
              showErrorMessage: true,
              errorStyle: 'error',
              errorTitle: 'Invalid Value',
              error: `Please select a value from the dropdown list`,
              showInputMessage: true,
              promptTitle: `Select ${fieldName.replace(/_/g, ' ')}`,
              prompt: `Choose from available options`
            };
          } catch (validationError) {
            console.warn(`Failed to set validation for ${fieldName} at row ${rowIndex}:`, validationError);
          }
        }
      }
    }
  });

  // Add borders
  worksheet.eachRow((row) => {
    row.eachCell((cell) => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });
  });
};

// Helper function to setup contacts sheet
const setupContactsSheet = (worksheet: ExcelJS.Worksheet, headers: string[], contactsData: any[]) => {
  // Set up headers
  const headerRow = worksheet.addRow(headers);
  headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
  headerRow.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: '70AD47' }
  };

  // Set column widths
  const columnWidths = [25, 25, 30, 20, 12, 12];
  columnWidths.forEach((width, index) => {
    worksheet.getColumn(index + 1).width = width;
  });

  // Add contacts data with proper handling of null/undefined values
  contactsData.forEach(data => {
    const values = Object.values(data).map(value => {
      // Handle null, undefined, and complex objects
      if (value === null || value === undefined) {
        return '';
      }
      if (typeof value === 'object') {
        return JSON.stringify(value);
      }
      return value;
    });
    const row = worksheet.addRow(values);
    row.font = { color: { argb: '000000' } };
  });

  // Add dropdown validation for boolean fields
  ['is_whatsapp', 'is_primary'].forEach(fieldName => {
    const columnIndex = headers.indexOf(fieldName) + 1;
    if (columnIndex > 0) {
      for (let rowIndex = 2; rowIndex <= Math.max(1000, contactsData.length + 100); rowIndex++) {
        const cell = worksheet.getCell(rowIndex, columnIndex);
        try {
          cell.dataValidation = {
            type: 'list',
            allowBlank: true,
            formulae: ['"true,false"'],
            showErrorMessage: true,
            errorStyle: 'error',
            errorTitle: 'Invalid Value',
            error: 'Please select true or false',
            showInputMessage: true,
            promptTitle: `Select ${fieldName.replace(/_/g, ' ')}`,
            prompt: 'Choose from: true, false'
          };
        } catch (validationError) {
          console.warn(`Failed to set boolean validation for ${fieldName} at row ${rowIndex}:`, validationError);
        }
      }
    }
  });

  // Add borders
  worksheet.eachRow((row) => {
    row.eachCell((cell) => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });
  });
};

// Helper function to setup category references sheet
const setupCategoryReferencesSheet = (worksheet: ExcelJS.Worksheet, headers: string[], categoryOptions: string[]) => {
  // Set up headers
  const headerRow = worksheet.addRow(headers);
  headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
  headerRow.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFC000' }
  };

  // Set column width
  worksheet.getColumn(1).width = 30;

  // Add instruction row
  const instructionRow = worksheet.addRow(['Available categories for reference']);
  instructionRow.font = { italic: true, color: { argb: '666666' } };

  // Add empty row for spacing
  worksheet.addRow(['']);

  // Add all available categories
  categoryOptions.forEach(category => {
    const row = worksheet.addRow([category]);
    row.font = { color: { argb: '666666' } };
  });

  // Add borders to all cells
  worksheet.eachRow((row) => {
    row.eachCell((cell) => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });
  });
};

// Helper function to setup language references sheet
const setupLanguageReferencesSheet = (worksheet: ExcelJS.Worksheet, headers: string[], languageOptions: string[]) => {
  // Set up headers
  const headerRow = worksheet.addRow(headers);
  headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
  headerRow.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFC000' }
  };

  // Set column width
  worksheet.getColumn(1).width = 30;

  // Add instruction row
  const instructionRow = worksheet.addRow(['Available language names for reference (use comma-separated for multiple)']);
  instructionRow.font = { italic: true, color: { argb: '666666' } };

  // Add empty row for spacing
  worksheet.addRow(['']);

  // Add all available languages
  languageOptions.forEach(language => {
    const row = worksheet.addRow([language]);
    row.font = { color: { argb: '666666' } };
  });

  // Add borders to all cells
  worksheet.eachRow((row) => {
    row.eachCell((cell) => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });
  });
};

// Main function to generate Excel export with full template compatibility
const generateExcelExportV2 = async (req: any, res: any, exportData: any[], allContacts: any[], timestamp: string, categoryMap: Map<string, string>) => {
  try {


    // Use category names from the passed categoryMap for dropdown options
    let categoryOptionsForDropdown: string[] = [];
    if (categoryMap.size > 0) {
      categoryOptionsForDropdown = Array.from(categoryMap.values()).sort();
    } else {
      // Fallback categories if no category mapping is available
      categoryOptionsForDropdown = ['Food Service', 'Accommodation', 'Transportation', 'Entertainment', 'Equipment Rental', 'Professional Services'];
    }

    // Create a new workbook
    const workbook = new ExcelJS.Workbook();

    // Sheet 1: Suppliers (matching import template headers exactly)
    const suppliersSheet = workbook.addWorksheet('Suppliers');

    // Use the headers from the first export record to maintain order and selection
    const supplierHeaders = exportData.length > 0 ? Object.keys(exportData[0]) : [
      "name", "supplier_type", "website", "status",
      "preference", "timezone", "language_preference",
      "payment_method", "payout_terms", "tax_id", "default_currency",
      "bank_account_details", "categories", "address"
    ];

    // Sheet 2: Contacts
    const contactsSheet = workbook.addWorksheet('Contacts');
    const contactHeaders = [
      'supplier_name',
      'contact_name',
      'contact_email',
      'contact_phone',
      'is_whatsapp',
      'is_primary'
    ];

    // Sheet 3: Category References
    const categoryReferencesSheet = workbook.addWorksheet('Category References');
    const categoryHeaders = ['category_name'];

    // Sheet 4: Language References
    const languageReferencesSheet = workbook.addWorksheet('Language References');
    const languageHeaders = ['language_name'];

    // Prepare language options from constants (use labels instead of codes)
    const languageOptions = LANGUAGES.map(lang => lang.label);

    // Setup all sheets
    setupSuppliersSheet(suppliersSheet, supplierHeaders, exportData);
    setupContactsSheet(contactsSheet, contactHeaders, allContacts);
    setupCategoryReferencesSheet(categoryReferencesSheet, categoryHeaders, categoryOptionsForDropdown);
    setupLanguageReferencesSheet(languageReferencesSheet, languageHeaders, languageOptions);

    // Set content type and headers for Excel file download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="suppliers_export_${timestamp}.xlsx"`);

    // Write the workbook to the response
    try {
      await workbook.xlsx.write(res);
    } catch (writeError) {
      console.error('Error writing Excel file:', writeError);
      throw new Error('Failed to write Excel file');
    }

  } catch (error) {
    console.error('Error generating Excel export:', error);
    if (!res.headersSent) {
      res.status(500).json({
        type: 'export_error',
        message: error instanceof Error ? error.message : 'Error generating Excel export'
      });
    }
  }
};

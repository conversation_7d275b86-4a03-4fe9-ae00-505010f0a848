import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import multer from 'multer';
import * as XLSX from 'xlsx';
import { LANGUAGES } from "../../../../../admin/constants/supplier-form-options";

// Helper function to normalize supplier type to validation schema format
const normalizeSupplierType = (supplierType: string): string => {
  if (!supplierType) return 'Company';

  const normalized = supplierType.toLowerCase().trim();
  const typeMap: { [key: string]: string } = {
    'company': 'Company',
    'individual': 'Individual'
  };

  return typeMap[normalized] || 'Company';
};

// Helper function to normalize status to validation schema format
const normalizeStatus = (status: string): string => {
  if (!status) return 'Active';

  const normalized = status.toLowerCase().trim();
  const statusMap: { [key: string]: string } = {
    'active': 'Active',
    'inactive': 'Inactive',
  };

  return statusMap[normalized] || 'Active';
};

// Note: business_type field removed from simplified data model

// Helper function to convert language labels to codes
const convertLanguageLabelsToCode = (languageString: string): string[] => {
  if (!languageString || languageString.trim() === '') {
    return [];
  }

  // Split by comma and process each language
  const languageLabels = languageString.split(',').map(label => label.trim()).filter(label => label);

  const languageCodes = languageLabels.map(label => {
    // First try exact match
    const exactMatch = LANGUAGES.find(lang => lang.label.toLowerCase() === label.toLowerCase());
    if (exactMatch) {
      return exactMatch.value;
    }

    // If no exact match, check if it's already a code
    const codeMatch = LANGUAGES.find(lang => lang.value.toLowerCase() === label.toLowerCase());
    if (codeMatch) {
      return codeMatch.value;
    }

    // If no match found, return the original value (for backwards compatibility)
    return label;
  });

  return languageCodes.filter(code => code); // Remove empty values
};

// Helper function to normalize categories from comma-separated string to array
const normalizeCategories = (categoriesString: string): string[] => {
  if (!categoriesString || categoriesString.trim() === '') {
    return [];
  }

  // Split by comma and clean up each category name
  return categoriesString
    .split(',')
    .map(category => category.trim())
    .filter(category => category.length > 0);
};

// Helper function to parse boolean values from Excel
const parseBooleanValue = (value: any): boolean => {
  if (value === null || value === undefined || value === '') {
    return false;
  }

  if (typeof value === 'boolean') {
    return value;
  }

  if (typeof value === 'number') {
    return value === 1;
  }

  if (typeof value === 'string') {
    const lowerValue = value.toLowerCase().trim();
    return lowerValue === 'true' || lowerValue === '1' || lowerValue === 'yes' || lowerValue === 'y';
  }

  return false;
};

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (_req: any, file: any, cb: any) => {
    // Accept Excel and CSV files
    if (
      file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.mimetype === 'application/vnd.ms-excel' ||
      file.mimetype === 'text/csv' ||
      file.mimetype === 'application/csv'
    ) {
      cb(null, true);
    } else {
      cb(new Error('Only Excel (.xlsx, .xls) and CSV files are allowed'));
    }
  },
});

/**
 * POST endpoint to parse supplier data from uploaded Excel/CSV file
 */
export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  // Use multer to handle the file upload
  const multerUpload = upload.single('file');

  multerUpload(req as any, res as any, async (err: any) => {
    if (err) {
      return res.status(400).json({ message: err.message });
    }

    if (!(req as any).file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    try {
      const file = (req as any).file;
      let workbook: XLSX.WorkBook;

      // Parse the file based on its type
      if (file.mimetype.includes('csv')) {
        // Parse CSV (single sheet only)
        const csvData = file.buffer.toString('utf8');
        workbook = XLSX.read(csvData, { type: 'string' });
      } else {
        // Parse Excel (multi-sheet support)
        workbook = XLSX.read(file.buffer, { type: 'buffer' });
      }

      // Parse suppliers from the first sheet (Suppliers)
      const suppliersSheetName = workbook.SheetNames.find(name =>
        name.toLowerCase().includes('supplier') || workbook.SheetNames.indexOf(name) === 0
      ) || workbook.SheetNames[0];

      const suppliersWorksheet = workbook.Sheets[suppliersSheetName];
      const suppliersJsonData = XLSX.utils.sheet_to_json(suppliersWorksheet, { header: 1 });

      if (suppliersJsonData.length < 2) {
        return res.status(400).json({
          message: 'Suppliers sheet must contain at least a header row and one data row'
        });
      }

      // Extract suppliers data
      const supplierHeaders = suppliersJsonData[0] as string[];
      const supplierDataRows = suppliersJsonData.slice(1) as any[][];

      // Check if we have the required headers
      const hasNameColumn = supplierHeaders.some(header =>
        header && header.toString().toLowerCase().trim() === 'name'
      );

      if (!hasNameColumn) {
        return res.status(400).json({
          message: `Missing required "name" column. Found headers: ${supplierHeaders.join(', ')}. Please ensure your file has a "name" column.`
        });
      }



      const suppliers = supplierDataRows
        .filter(row => row.some(cell => cell !== null && cell !== undefined && cell !== ''))
        .map((row, rowIndex) => {
          const supplier: any = {};
          supplierHeaders.forEach((header, headerIndex) => {
            if (header && row[headerIndex] !== undefined && row[headerIndex] !== null) {
              supplier[header.trim()] = row[headerIndex];
            }
          });



          // Skip rows that don't have essential data (only name is required now)
          if (!supplier.name || supplier.name.toString().trim() === '') {
            return null;
          }

          // Clean up and validate data
          const rawSupplierType = supplier.supplier_type?.toString().trim() || '';
          const rawStatus = supplier.status?.toString().trim() || '';
          const normalizedSupplierType = normalizeSupplierType(rawSupplierType);
          const normalizedStatus = normalizeStatus(rawStatus);



          const cleanedSupplier = {
            name: supplier.name?.toString().trim(),
            handle: supplier.handle?.toString().trim() || supplier.name?.toString().toLowerCase().replace(/[^a-z0-9\s-]/g, '').replace(/\s+/g, '-'),
            supplier_type: normalizedSupplierType,
            website: supplier.website?.toString().trim(),
            status: normalizedStatus,
            preference: supplier.preference?.toString().trim(),
            timezone: supplier.timezone?.toString().trim(),
            language_preference: convertLanguageLabelsToCode(supplier.language_preference?.toString() || ''),
            payment_method: supplier.payment_method?.toString().trim(),
            payout_terms: supplier.payout_terms?.toString().trim(),
            tax_id: supplier.tax_id?.toString().trim(),
            default_currency: supplier.default_currency?.toString().trim(),
            bank_account_details: supplier.bank_account_details?.toString().trim(),
            categories: normalizeCategories(supplier.categories?.toString() || ''),
            address: supplier.address?.toString().trim(),
          };



          return cleanedSupplier;
        })
        .filter(supplier => supplier !== null);

      // Parse contacts from the Contacts sheet (if exists)
      let contacts: any[] = [];
      const contactsSheetName = workbook.SheetNames.find(name =>
        name.toLowerCase().includes('contact')
      );

      if (contactsSheetName) {
        const contactsWorksheet = workbook.Sheets[contactsSheetName];
        const contactsJsonData = XLSX.utils.sheet_to_json(contactsWorksheet, { header: 1 });

        if (contactsJsonData.length >= 2) {
          const contactHeaders = contactsJsonData[0] as string[];
          const contactDataRows = contactsJsonData.slice(1) as any[][];

          contacts = contactDataRows
            .filter(row => row.some(cell => cell !== null && cell !== undefined && cell !== ''))
            .map((row, rowIndex) => {
              const contact: any = {};
              contactHeaders.forEach((header, headerIndex) => {
                if (header && row[headerIndex] !== undefined && row[headerIndex] !== null) {
                  contact[header.trim()] = row[headerIndex];
                }
              });

              // Skip rows that don't have essential contact data
              if (!contact.contact_name || !contact.supplier_name) {
                return null;
              }

              // Clean up contact data
              const cleanedContact = {
                supplier_name: contact.supplier_name?.toString().trim(),
                name: contact.contact_name?.toString().trim(), // Map to 'name' for service compatibility
                email: contact.contact_email?.toString().trim(),
                phone: contact.contact_phone?.toString().trim(),
                is_whatsapp: parseBooleanValue(contact.is_whatsapp),
                is_primary: parseBooleanValue(contact.is_primary),
              };

              return cleanedContact;
            })
            .filter(contact => contact !== null);
        }
      }

      // Group contacts by supplier_name
      const contactsBySupplier: { [key: string]: any[] } = {};
      contacts.forEach(contact => {
        if (!contactsBySupplier[contact.supplier_name]) {
          contactsBySupplier[contact.supplier_name] = [];
        }
        contactsBySupplier[contact.supplier_name].push(contact);
      });

      // Attach contacts to suppliers based on name matching
      const suppliersWithContacts = suppliers.map(supplier => ({
        ...supplier,
        contacts: contactsBySupplier[supplier.name] || []
      }));

      if (suppliersWithContacts.length === 0) {
        return res.status(400).json({
          message: `No valid supplier data found in the file. Found ${supplierDataRows.length} data rows but none had valid supplier names. Please ensure:
          1. The file has a "name" column with supplier names
          2. The supplier names are not empty
          3. The file format matches the template structure

          Headers found: ${supplierHeaders.join(', ')}`
        });
      }

      res.json({
        success: true,
        suppliers: suppliersWithContacts,
        contacts,
        count: suppliersWithContacts.length,
        contactsCount: contacts.length,
        message: `Successfully parsed ${suppliersWithContacts.length} suppliers with ${contacts.length} contacts from file`
      });

    } catch (error) {
      let errorMessage = 'Error parsing file. Please ensure the file is a valid Excel or CSV file.';
      let errorDetails = [];

      if (error instanceof Error) {
        errorMessage = `Parse error: ${error.message}`;
        errorDetails.push(error.stack || error.message);
      } else if (typeof error === 'string') {
        errorMessage = `Parse error: ${error}`;
      }

      res.status(500).json({
        success: false,
        message: errorMessage,
        details: errorDetails.join('\n'),
        errors: errorDetails,
        type: 'parse_error'
      });
    }
  });
};

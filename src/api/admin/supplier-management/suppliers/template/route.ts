import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import * as ExcelJS from "exceljs";
import { SUPPLIER_MANAGEMENT_MODULE } from "src/modules/vendor_management";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierManagementModuleService from "src/modules/vendor_management/supplier-service";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";
import {
  LANGUAGES,
  TIMEZONES,
  PAYMENT_METHODS,
  PAYOUT_TERMS,
  CURRENCIES,
  SUPPLIER_STATUSES,
  SUPPLIER_TYPES
} from "../../../../../admin/constants/supplier-form-options";

// Helper function to normalize business type to proper capitalization
const normalizeBusinessType = (
  businessType: string,
  businessTypeOptions: string[]
): string => {
  if (!businessType) return "";

  // First try to find exact match in the API business types (proper capitalization)
  const exactMatch = businessTypeOptions.find(
    (option) => option.toLowerCase() === businessType.toLowerCase()
  );

  if (exactMatch) {
    return exactMatch;
  }

  // If no match found, capitalize the first letter of each word
  return businessType
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

// Helper function to setup suppliers sheet with formatting and dropdowns (matching export functionality)
const setupSuppliersSheet = (
  worksheet: ExcelJS.Worksheet,
  headers: string[]
) => {
  // Set up headers with proper formatting (matching export style)
  const headerRow = worksheet.addRow(headers);
  headerRow.font = { bold: true, color: { argb: "FFFFFF" } };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "4472C4" }, // Match export header color
  };

  // Set column widths (matching new simplified field structure)
  // Headers: name, supplier_type, website, status, preference, timezone, language_preference, payment_method, payout_terms, tax_id, default_currency, bank_account_details, categories, address
  const columnWidths = [25, 15, 30, 12, 15, 20, 25, 20, 15, 15, 15, 30, 25, 40];
  columnWidths.forEach((width, index) => {
    worksheet.getColumn(index + 1).width = width;
  });

  // Add only ONE example row (clear content but keep 1 example)
  const exampleData = [
    {
      name: "Example Supplier Ltd",
      supplier_type: "Company", // Must match validation schema: "Company" or "Individual"
      website: "https://www.example-supplier.com",
      status: "Active", // Must match validation schema: "Active", "Inactive", etc.
      preference: "Preferred", // Must be "Preferred" or "Backup"
      timezone: "UTC-5 (Eastern Time)",
      language_preference: "English,Spanish",
      payment_method: "Bank Transfer",
      payout_terms: "Net 30",
      tax_id: "TAX123456789",
      default_currency: "USD",
      bank_account_details: "Bank: Example Bank, Account: **********",
      categories: "food_service,catering",
      address: "123 Business Street, Suite 100\nNew York, NY 10001\nUnited States",
    },
  ];

  // Add the single example row with proper handling of null/undefined values
  // Ensure the values are in the same order as headers
  exampleData.forEach((data) => {
    const values = headers.map(header => {
      const value = data[header];
      // Handle null, undefined, and complex objects
      if (value === null || value === undefined) {
        return '';
      }
      if (typeof value === 'object') {
        return JSON.stringify(value);
      }
      return value;
    });
    const row = worksheet.addRow(values);
    row.font = { color: { argb: "000000" } }; // Match export row color
  });

  // Apply dropdown validation using options from supplier-form-options (matching export functionality exactly)
  const dropdownOptions = {
    supplier_type: SUPPLIER_TYPES.map(option => option.value),
    status: SUPPLIER_STATUSES.map(option => option.value),
    preference: ['Preferred', 'Backup'], // Static preference options
    timezone: TIMEZONES.map(option => option.value),
    payment_method: PAYMENT_METHODS.map(option => option.value),
    payout_terms: PAYOUT_TERMS.map(option => option.value),
    default_currency: CURRENCIES.map(option => option.value) // Currency codes only
  };

  Object.entries(dropdownOptions).forEach(([fieldName, options]) => {
    const columnIndex = headers.indexOf(fieldName) + 1;
    if (columnIndex > 0 && options.length > 0) {
      // Clean and sanitize options to prevent Excel corruption (matching export logic)
      const cleanOptions = options
        .filter(option => option && typeof option === 'string')
        .map(option => option.toString().trim())
        .filter(option => option.length > 0)
        .slice(0, 50); // Limit to 50 options to prevent Excel issues

      if (cleanOptions.length === 0) {
        return;
      }

      // Create a safe formula for dropdown validation
      // Excel has a limit of 255 characters for direct list validation
      const optionsString = cleanOptions.join(',');

      // If the options string is too long, we'll use a different approach
      if (optionsString.length > 255) {
        // Use only the first few options that fit within the limit
        let truncatedOptions = [];
        let currentLength = 0;
        for (const option of cleanOptions) {
          if (currentLength + option.length + 1 <= 250) { // Leave some buffer
            truncatedOptions.push(option);
            currentLength += option.length + 1; // +1 for comma
          } else {
            break;
          }
        }

        if (truncatedOptions.length === 0) {
          return;
        }

        for (let rowIndex = 2; rowIndex <= Math.max(1000, 100); rowIndex++) {
          const cell = worksheet.getCell(rowIndex, columnIndex);
          try {
            cell.dataValidation = {
              type: 'list',
              allowBlank: true,
              formulae: [`"${truncatedOptions.join(',')}"`],
              showErrorMessage: true,
              errorStyle: 'error',
              errorTitle: 'Invalid Value',
              error: `Please select a value from the dropdown list`,
              showInputMessage: true,
              promptTitle: `Select ${fieldName.replace(/_/g, ' ')}`,
              prompt: `Choose from available options`
            };
          } catch (validationError) {
            // Silently continue if validation fails
          }
        }
      } else {
        // Standard approach for shorter option lists
        for (let rowIndex = 2; rowIndex <= Math.max(1000, 100); rowIndex++) {
          const cell = worksheet.getCell(rowIndex, columnIndex);
          try {
            cell.dataValidation = {
              type: 'list',
              allowBlank: true,
              formulae: [`"${cleanOptions.join(',')}"`],
              showErrorMessage: true,
              errorStyle: 'error',
              errorTitle: 'Invalid Value',
              error: `Please select a value from the dropdown list`,
              showInputMessage: true,
              promptTitle: `Select ${fieldName.replace(/_/g, ' ')}`,
              prompt: `Choose from available options`
            };
          } catch (validationError) {
            // Silently continue if validation fails
          }
        }
      }
    }
  });

  // Add borders
  worksheet.eachRow((row) => {
    row.eachCell((cell) => {
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };
    });
  });
};

// Helper function to setup contacts sheet (matching export functionality)
const setupContactsSheet = (
  worksheet: ExcelJS.Worksheet,
  headers: string[]
) => {
  // Set up headers (matching export style)
  const headerRow = worksheet.addRow(headers);
  headerRow.font = { bold: true, color: { argb: "FFFFFF" } };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "70AD47" }, // Match export contacts header color
  };

  // Set column widths (matching export column widths)
  const columnWidths = [25, 25, 30, 20, 12, 12];
  columnWidths.forEach((width, index) => {
    worksheet.getColumn(index + 1).width = width;
  });

  // Add only ONE example contact (clear content but keep 1 example)
  const exampleContacts = [
    {
      supplier_name: "Example Supplier Ltd",
      contact_name: "John Smith",
      contact_email: "<EMAIL>",
      contact_phone: "+****************",
      is_whatsapp: "false", // String format for Excel compatibility
      is_primary: "true",   // String format for Excel compatibility
    },
  ];

  // Add contacts data with proper handling of null/undefined values (matching export logic)
  exampleContacts.forEach((data) => {
    const values = Object.values(data).map(value => {
      // Handle null, undefined, and complex objects
      if (value === null || value === undefined) {
        return '';
      }
      if (typeof value === 'object') {
        return JSON.stringify(value);
      }
      return value;
    });
    const row = worksheet.addRow(values);
    row.font = { color: { argb: "000000" } }; // Match export row color
  });

  // Add dropdown validation for boolean fields (matching export logic)
  ['is_whatsapp', 'is_primary'].forEach(fieldName => {
    const columnIndex = headers.indexOf(fieldName) + 1;
    if (columnIndex > 0) {
      for (let rowIndex = 2; rowIndex <= Math.max(1000, 100); rowIndex++) {
        const cell = worksheet.getCell(rowIndex, columnIndex);
        try {
          cell.dataValidation = {
            type: 'list',
            allowBlank: true,
            formulae: ['"true,false"'],
            showErrorMessage: true,
            errorStyle: 'error',
            errorTitle: 'Invalid Value',
            error: 'Please select true or false',
            showInputMessage: true,
            promptTitle: `Select ${fieldName.replace(/_/g, ' ')}`,
            prompt: 'Choose from: true, false'
          };
        } catch (validationError) {
          // Silently continue if validation fails
        }
      }
    }
  });

  // Add borders
  worksheet.eachRow((row) => {
    row.eachCell((cell) => {
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };
    });
  });
};

// Helper function to setup category references sheet (matching export functionality)
const setupCategoryReferencesSheet = (
  worksheet: ExcelJS.Worksheet,
  headers: string[],
  categoryOptions: string[]
) => {
  // Ensure we always have categories to display
  if (!categoryOptions || categoryOptions.length === 0) {
    categoryOptions = ['Food Service', 'Accommodation', 'Transportation', 'Entertainment', 'Equipment Rental', 'Professional Services'];
  }

  // Set up headers (matching export style)
  const headerRow = worksheet.addRow(headers);
  headerRow.font = { bold: true, color: { argb: "FFFFFF" } };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFC000" }, // Match export category references header color
  };

  // Set column width for better readability
  worksheet.getColumn(1).width = 35;

  // Add comprehensive instruction rows
  const instructionRow1 = worksheet.addRow(['Available service categories for reference (use comma-separated for multiple)']);
  instructionRow1.font = { italic: true, color: { argb: '666666' } };

  const instructionRow2 = worksheet.addRow(['Example: "Food Service,Entertainment" or single category: "Accommodation"']);
  instructionRow2.font = { italic: true, color: { argb: '888888' }, size: 10 };

  // Add empty row for spacing
  worksheet.addRow(['']);

  // Add category count info
  const countRow = worksheet.addRow([`${categoryOptions.length} categories available:`]);
  countRow.font = { bold: true, color: { argb: '444444' }, size: 11 };

  // Add empty row for spacing
  worksheet.addRow(['']);

  // Add all available categories with consistent formatting
  categoryOptions.forEach((category, index) => {
    const row = worksheet.addRow([category]);
    row.font = { color: { argb: "333333" } };

    // Alternate row background for better readability
    if (index % 2 === 0) {
      row.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "F8F9FA" }
      };
    }
  });

  // Add borders to all cells for professional appearance
  worksheet.eachRow((row) => {
    row.eachCell((cell) => {
      cell.border = {
        top: { style: "thin", color: { argb: "CCCCCC" } },
        left: { style: "thin", color: { argb: "CCCCCC" } },
        bottom: { style: "thin", color: { argb: "CCCCCC" } },
        right: { style: "thin", color: { argb: "CCCCCC" } },
      };
    });
  });
};

// Helper function to setup language references sheet (matching export functionality)
const setupLanguageReferencesSheet = (
  worksheet: ExcelJS.Worksheet,
  headers: string[],
  languageOptions: string[]
) => {
  // Set up headers (matching export style)
  const headerRow = worksheet.addRow(headers);
  headerRow.font = { bold: true, color: { argb: "FFFFFF" } };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFC000" }, // Match export language references header color
  };

  // Set column width
  worksheet.getColumn(1).width = 30;

  // Add instruction row (matching export style)
  const instructionRow = worksheet.addRow(['Available language names for reference (use comma-separated for multiple)']);
  instructionRow.font = { italic: true, color: { argb: '666666' } };

  // Add empty row for spacing
  worksheet.addRow(['']);

  // Add all available languages
  languageOptions.forEach((language) => {
    const row = worksheet.addRow([language]);
    row.font = { color: { argb: "666666" } };
  });

  // Add borders to all cells
  worksheet.eachRow((row) => {
    row.eachCell((cell) => {
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };
    });
  });
};

/**
 * GET endpoint to download a supplier import template
 * Template has same functionality as export Excel file including dropdown values which are dynamically fetched
 * Content is cleared with just 1 example but cell dropdown values are dynamically updated each time
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {

    // Fetch categories with robust error handling - always ensure Category References sheet is populated
    let categoryOptions: string[] = [];
    let categoriesSource = 'fallback';

    try {
      const supplierProductsServicesService: SupplierProductsServicesModuleService =
        req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

      const categoriesResult = await supplierProductsServicesService.listCategories(
        { is_active: true },
        { skip: 0, take: 1000 }
      );

      // Handle different return formats from generated method
      let categories: any[] = [];
      if (Array.isArray(categoriesResult)) {
        categories = categoriesResult;
      } else if (categoriesResult && typeof categoriesResult === 'object') {
        const result = categoriesResult as any;
        if ('categories' in result && Array.isArray(result.categories)) {
          categories = result.categories;
        } else if ('data' in result && Array.isArray(result.data)) {
          categories = result.data;
        }
      }

      if (categories && categories.length > 0) {
        categoryOptions = categories
          .map((category: any) => category.name)
          .filter((name: string) => name && name.trim() !== '')
          .sort();

        if (categoryOptions.length > 0) {
          categoriesSource = 'database';
        }
      }
    } catch (error) {
      // Silently fall back to default categories
    }

    // Always ensure Category References sheet has data - use fallback if database fetch failed
    if (categoryOptions.length === 0) {
      categoryOptions = ['Food Service', 'Accommodation', 'Transportation', 'Entertainment', 'Equipment Rental', 'Professional Services'];
    }

    // Ensure we always have at least some categories for the template
    if (categoryOptions.length === 0) {
      throw new Error('No categories available for template generation - this should never happen');
    }

    // Create a new workbook
    const workbook = new ExcelJS.Workbook();

    // Sheet 1: Suppliers (matching import template headers exactly)
    const suppliersSheet = workbook.addWorksheet('Suppliers');
    const supplierHeaders = [
      "name", "supplier_type", "website", "status",
      "preference", "timezone", "language_preference",
      "payment_method", "payout_terms", "tax_id", "default_currency",
      "bank_account_details", "categories", "address"
    ];

    // Sheet 2: Contacts
    const contactsSheet = workbook.addWorksheet('Contacts');
    const contactHeaders = [
      'supplier_name',
      'contact_name',
      'contact_email',
      'contact_phone',
      'is_whatsapp',
      'is_primary'
    ];

    // Sheet 3: Category References
    const categoryReferencesSheet = workbook.addWorksheet('Category References');
    const categoryHeaders = ['category_name'];

    // Sheet 4: Language References
    const languageReferencesSheet = workbook.addWorksheet('Language References');
    const languageHeaders = ['language_name'];

    // Prepare language options from constants (use labels instead of codes)
    const languageOptions = LANGUAGES.map(lang => lang.label);

    // Setup all sheets
    try {
      setupSuppliersSheet(suppliersSheet, supplierHeaders);
      setupContactsSheet(contactsSheet, contactHeaders);
      setupCategoryReferencesSheet(categoryReferencesSheet, categoryHeaders, categoryOptions);
      setupLanguageReferencesSheet(languageReferencesSheet, languageHeaders, languageOptions);
    } catch (sheetError) {
      throw new Error(`Failed to setup template sheets: ${sheetError.message}`);
    }

    // Set content type and headers for Excel file download
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    );
    res.setHeader(
      'Content-Disposition',
      'attachment; filename=supplier-import-template.xlsx'
    );

    // Write the workbook to the response
    try {
      await workbook.xlsx.write(res);
      res.end(); // Ensure the response is properly ended
    } catch (writeError) {
      throw new Error('Failed to write Excel template file');
    }

  } catch (error) {
    if (!res.headersSent) {
      res.status(500).json({
        type: 'template_error',
        message: error instanceof Error ? error.message : 'Error generating template'
      });
    }
  }
};

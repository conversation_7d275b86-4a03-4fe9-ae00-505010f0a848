import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { BOOKING_ADD_ONS_MODULE } from "../../../modules/booking-add-ons";

// Validation schema for creating booking add-ons
const CreateBookingAddOnSchema = z.object({
  order_id: z.string().min(1, "Order ID is required"),
  add_on_variant_id: z.string().min(1, "Add-on variant ID is required"),
  add_on_name: z.string().min(1, "Add-on name is required"),
  quantity: z.number().int().min(1, "Quantity must be at least 1").default(1),
  unit_price: z.number().min(0, "Unit price must be non-negative"),
  currency_code: z.string().default("CHF"),
  customer_field_responses: z.record(z.any()).default({}),
  add_on_metadata: z.record(z.any()).default({}),
});

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log("🔍 Fetching booking add-ons using proper service...");

    // Extract query parameters
    const { order_id, limit = 50, offset = 0 } = req.query;

    // Resolve the booking add-on service
    const bookingAddOnService = req.scope.resolve(BOOKING_ADD_ONS_MODULE);
    console.log("✅ BookingAddOnService resolved successfully");

    // If order_id is provided, get add-ons for that specific order
    if (order_id) {
      console.log(`🔍 Fetching add-ons for specific order: ${order_id}`);
      const result = await bookingAddOnService.getBookingAddOns(
        order_id as string
      );

      res.json({
        booking_addons: result.booking_add_ons,
        count: result.count,
        offset: parseInt(offset as string),
        limit: parseInt(limit as string),
      });
      return;
    }

    // Otherwise, get all booking add-ons with related data
    console.log("🔍 Fetching all booking add-ons with related data...");

    try {
      // Use the new getAllBookingAddOns method that includes related data
      const result = await bookingAddOnService.getAllBookingAddOns({
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
      });

      console.log(`✅ Found ${result.count} booking add-ons with related data`);

      // Transform the data and fetch related information
      const query = req.scope.resolve("query");

      const formattedAddons = await Promise.all(
        result.booking_add_ons.map(async (addon: any) => {
          // Fetch order data
          let orderData = null;
          if (addon.order_id) {
            try {
              console.log(`🔍 Fetching order data for: ${addon.order_id}`);
              const { data: orders } = await query.graph({
                entity: "order",
                filters: { id: addon.order_id },
                fields: [
                  "id",
                  "display_id",
                  "customer_id",
                  "email",
                  "metadata",
                ],
              });
              orderData = orders?.[0] || null;
              console.log(`✅ Order data:`, orderData ? "Found" : "Not found");
            } catch (orderError) {
              console.log("❌ Could not fetch order data:", orderError);
            }
          }

          // Fetch add-on variant data
          let addOnData = null;
          if (addon.add_on_variant_id) {
            try {
              console.log(
                `🔍 Fetching variant data for: ${addon.add_on_variant_id}`
              );
              const { data: variants } = await query.graph({
                entity: "product_variant",
                filters: { id: addon.add_on_variant_id },
                fields: ["id", "title", "metadata"],
              });
              addOnData = variants?.[0] || null;
              console.log(
                `✅ Variant data:`,
                addOnData ? "Found" : "Not found"
              );
            } catch (variantError) {
              console.log("❌ Could not fetch variant data:", variantError);
            }
          }

          // Debug: Log booking add-on sync status
          console.log(`🔍 Booking Add-on Debug:`, {
            id: addon.id,
            add_on_name: addon.add_on_name,
            supplier_order_id: addon.supplier_order_id,
            order_status: addon.order_status,
            created_at: addon.created_at,
            updated_at: addon.updated_at,
          });
          return {
            id: addon.id,
            order_id: addon.order_id,
            add_on_id: addon.add_on_variant_id,
            add_on_name: addon.add_on_name,
            quantity: addon.quantity,
            unit_price: parseFloat(addon.unit_price || 0),
            total_price: parseFloat(addon.total_price || 0),
            customer_field_responses: addon.customer_field_responses || {},
            // Include supplier order tracking fields
            supplier_order_id: addon.supplier_order_id || null,
            order_status: addon.order_status || null,
            created_at: addon.created_at,
            updated_at: addon.updated_at,
            order: orderData,
            add_on: addOnData
              ? {
                  id: addOnData.id,
                  name: addOnData.title,
                  metadata: addOnData.metadata || {},
                }
              : null,
          };
        })
      );

      res.json({
        booking_addons: formattedAddons,
        count: result.count,
        offset: parseInt(offset as string),
        limit: parseInt(limit as string),
      });
    } catch (serviceError) {
      console.log(
        "Service method failed, returning empty result:",
        serviceError
      );

      // Return empty result if service method fails
      res.json({
        booking_addons: [],
        count: 0,
        offset: parseInt(offset as string),
        limit: parseInt(limit as string),
      });
    }
  } catch (error) {
    console.error("Error fetching booking add-ons:", error);
    res.status(500).json({
      type: "server_error",
      message:
        error instanceof Error
          ? error.message
          : "Failed to fetch booking add-ons",
    });
  }
};

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log("🔍 Creating new booking add-on...");

    // Validate request body
    const validationResult = CreateBookingAddOnSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        type: "validation_error",
        message: "Invalid request data",
        errors: validationResult.error.errors,
      });
    }

    const data = validationResult.data;
    console.log("✅ Validated booking add-on data:", data);
    console.log("🔍 Variant ID being used:", data.add_on_variant_id);

    // Resolve the booking add-on service
    const bookingAddOnService = req.scope.resolve(BOOKING_ADD_ONS_MODULE);

    // Check if the order exists
    const query = req.scope.resolve("query");
    const { data: orders } = await query.graph({
      entity: "order",
      filters: { id: data.order_id },
      fields: ["id", "display_id", "email"],
    });

    if (!orders || orders.length === 0) {
      return res.status(404).json({
        type: "not_found",
        message: `Order with ID ${data.order_id} not found`,
      });
    }

    // Check if the add-on variant exists
    const { data: variants } = await query.graph({
      entity: "product_variant",
      filters: { id: data.add_on_variant_id },
      fields: ["id", "title", "product_id"],
    });

    if (!variants || variants.length === 0) {
      return res.status(404).json({
        type: "not_found",
        message: `Add-on variant with ID ${data.add_on_variant_id} not found`,
      });
    }

    // Create the booking add-on
    const bookingAddOn = await bookingAddOnService.createBookingAddOn(data as any);

    console.log("✅ Booking add-on created successfully:", bookingAddOn.id);

    return res.status(201).json({
      success: true,
      message: "Booking add-on created successfully",
      booking_add_on: bookingAddOn,
    });

  } catch (error) {
    console.error("❌ Error creating booking add-on:", error);
    return res.status(500).json({
      type: "server_error",
      message: error instanceof Error ? error.message : "Failed to create booking add-on",
    });
  }
};

-- Create supplier_offering table
CREATE TABLE IF NOT EXISTS supplier_offering (
  id varchar(255) NOT NULL,
  product_service_id varchar(255) NOT NULL,
  supplier_id varchar(255) NOT NULL,
  active_from timestamptz NULL,
  active_to timestamptz NULL,
  availability_notes text NULL,
  status varchar(255) NOT NULL DEFAULT 'active',
  custom_fields jsonb NULL,
  created_by varchar(255) NULL,
  updated_by varchar(255) NULL,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now(),
  deleted_at timestamptz NULL,
  CONSTRAINT supplier_offering_pkey PRIMARY KEY (id),
  CONSTRAINT supplier_offering_status_check CHECK (status IN ('active', 'inactive'))
);

-- Create indexes for supplier_offering table
CREATE INDEX IF NOT EXISTS IDX_supplier_offering_product_service_id 
ON supplier_offering (product_service_id) 
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS IDX_supplier_offering_supplier_id 
ON supplier_offering (supplier_id) 
WHERE deleted_at IS NULL;

CREATE UNIQUE INDEX IF NOT EXISTS IDX_supplier_offering_unique 
ON supplier_offering (product_service_id, supplier_id) 
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS IDX_supplier_offering_status 
ON supplier_offering (status) 
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS IDX_supplier_offering_validity 
ON supplier_offering (active_from, active_to) 
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS IDX_supplier_offering_created_by 
ON supplier_offering (created_by) 
WHERE deleted_at IS NULL;

-- Add foreign key constraint to product_service table
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'supplier_offering_product_service_id_foreign'
  ) THEN
    ALTER TABLE supplier_offering 
    ADD CONSTRAINT supplier_offering_product_service_id_foreign 
    FOREIGN KEY (product_service_id) REFERENCES product_service(id) 
    ON DELETE CASCADE ON UPDATE CASCADE;
  END IF;
END
$$;

-- Verify the table was created
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name = 'supplier_offering';
